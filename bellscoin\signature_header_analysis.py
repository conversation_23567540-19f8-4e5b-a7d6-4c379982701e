#!/usr/bin/env python3
"""
Signature Header Analysis
========================

Found an interesting connection! In key.cpp, the SignCompact function mentions:
- 0x1B = first key with even y
- 0x1C = first key with odd y  
- 0x1D = second key with even y
- 0x1E = second key with odd y

The genesis script constant is 486604799 = 0x1d00ffff
The nBits value is 0x1e0ffff0

Both contain the signature header bytes 0x1D and 0x1E!
This might be the key to the treasure hunt.
"""

import hashlib
from ecdsa import SigningKey, SECP256k1
from ecdsa.util import string_to_number, number_to_string

# Target genesis public key
TARGET_PUBKEY = "040184710fa689ad5023690c80f3a49c8f13f8d45b8c857fbcbc8bc4a8e4d3eb4b10f4d4604fa08dce601aaf0f470216fe1b51850b4acf21b179c45070ac7b03a9"

# Signature header bytes from key.cpp
SIG_HEADERS = [0x1B, 0x1C, 0x1D, 0x1E]

# Genesis values containing these headers
SCRIPT_CONSTANT = 486604799  # 0x1d00ffff - contains 0x1D
NBITS = 0x1e0ffff0          # contains 0x1E

def test_private_key(private_key_hex: str) -> bool:
    """Test if a private key generates the target public key"""
    try:
        if len(private_key_hex) != 64:
            private_key_hex = private_key_hex.zfill(64)
        
        private_key_int = int(private_key_hex, 16)
        if private_key_int == 0 or private_key_int >= SECP256k1.order:
            return False
        
        private_key_bytes = number_to_string(private_key_int, SECP256k1.order)
        signing_key = SigningKey.from_string(private_key_bytes, curve=SECP256k1)
        verifying_key = signing_key.get_verifying_key()
        
        uncompressed_pubkey = "04" + verifying_key.to_string().hex()
        return uncompressed_pubkey.lower() == TARGET_PUBKEY.lower()
    except:
        return False

def sha256_hash(data: str) -> str:
    """Generate SHA256 hash of string"""
    return hashlib.sha256(data.encode()).hexdigest()

def analyze_signature_headers():
    """Analyze the signature header bytes as clues"""
    print("🔍 ANALYZING SIGNATURE HEADER BYTES")
    print("=" * 50)
    
    print("Signature header bytes from key.cpp:")
    print("0x1B = first key with even y")
    print("0x1C = first key with odd y")
    print("0x1D = second key with even y")
    print("0x1E = second key with odd y")
    print()
    print(f"Script constant: {SCRIPT_CONSTANT} = 0x{SCRIPT_CONSTANT:08x} (contains 0x1D)")
    print(f"nBits value: 0x{NBITS:08x} (contains 0x1E)")
    
    # Test individual header bytes
    for header in SIG_HEADERS:
        print(f"\n🔍 Testing header byte 0x{header:02x} = {header}")
        
        # Test various representations
        test_values = [
            str(header),
            f"{header:02x}",
            f"Nintondo{header}",
            f"Nintondo{header:02x}",
        ]
        
        for value in test_values:
            hash_result = sha256_hash(value)
            if test_private_key(hash_result):
                print(f"🎯 TREASURE FOUND! Header {header:02x}: {value}")
                return hash_result
    
    return None

def analyze_header_combinations():
    """Analyze combinations of header bytes"""
    print(f"\n🔍 ANALYZING HEADER COMBINATIONS")
    print("=" * 50)
    
    # Test pairs of headers
    for i, h1 in enumerate(SIG_HEADERS):
        for j, h2 in enumerate(SIG_HEADERS):
            if i != j:
                # Combine as hex
                combined_hex = f"{h1:02x}{h2:02x}"
                combined_dec = str(h1) + str(h2)
                
                test_values = [
                    combined_hex,
                    combined_dec,
                    f"Nintondo{combined_hex}",
                    f"Nintondo{combined_dec}",
                ]
                
                for value in test_values:
                    hash_result = sha256_hash(value)
                    if test_private_key(hash_result):
                        print(f"🎯 TREASURE FOUND! Headers {h1:02x}+{h2:02x}: {value}")
                        return hash_result
    
    # Test all four headers combined
    all_hex = "".join(f"{h:02x}" for h in SIG_HEADERS)  # "1b1c1d1e"
    all_dec = "".join(str(h) for h in SIG_HEADERS)      # "27282930"
    
    test_values = [
        all_hex,
        all_dec,
        f"Nintondo{all_hex}",
        f"Nintondo{all_dec}",
    ]
    
    for value in test_values:
        hash_result = sha256_hash(value)
        if test_private_key(hash_result):
            print(f"🎯 TREASURE FOUND! All headers: {value}")
            return hash_result
    
    return None

def analyze_header_in_constants():
    """Analyze how the headers appear in the genesis constants"""
    print(f"\n🔍 ANALYZING HEADERS IN GENESIS CONSTANTS")
    print("=" * 50)
    
    # Extract the header bytes from the constants
    script_header = (SCRIPT_CONSTANT >> 24) & 0xFF  # 0x1D
    nbits_header = (NBITS >> 24) & 0xFF             # 0x1E
    
    print(f"Header from script constant: 0x{script_header:02x}")
    print(f"Header from nBits: 0x{nbits_header:02x}")
    
    # Test the extracted headers
    test_values = [
        f"{script_header:02x}",
        f"{nbits_header:02x}",
        f"{script_header:02x}{nbits_header:02x}",
        f"{nbits_header:02x}{script_header:02x}",
        str(script_header),
        str(nbits_header),
        str(script_header) + str(nbits_header),
        str(nbits_header) + str(script_header),
        f"Nintondo{script_header:02x}",
        f"Nintondo{nbits_header:02x}",
        f"Nintondo{script_header:02x}{nbits_header:02x}",
        f"Nintondo{nbits_header:02x}{script_header:02x}",
    ]
    
    for value in test_values:
        hash_result = sha256_hash(value)
        if test_private_key(hash_result):
            print(f"🎯 TREASURE FOUND! Extracted headers: {value}")
            return hash_result
    
    return None

def analyze_signature_recovery_clue():
    """Analyze the signature recovery concept as a clue"""
    print(f"\n🔍 ANALYZING SIGNATURE RECOVERY CLUE")
    print("=" * 50)
    
    # The comment mentions "second key" for 0x1D and 0x1E
    # Maybe the private key is related to the "second" concept
    
    test_strings = [
        "second",
        "secondkey",
        "Nintondosecond",
        "Nintondosecondkey",
        "second1d",
        "second1e",
        "Nintondosecond1d",
        "Nintondosecond1e",
        "1dsecond",
        "1esecond",
        "Nintondo1dsecond",
        "Nintondo1esecond",
        "secondkey1d1e",
        "Nintondosecondkey1d1e",
    ]
    
    for test_str in test_strings:
        hash_result = sha256_hash(test_str)
        if test_private_key(hash_result):
            print(f"🎯 TREASURE FOUND! Recovery clue: {test_str}")
            return hash_result
    
    return None

def analyze_even_odd_clue():
    """Analyze the even/odd y coordinate clue"""
    print(f"\n🔍 ANALYZING EVEN/ODD Y COORDINATE CLUE")
    print("=" * 50)
    
    # The headers indicate even/odd y coordinates
    # Maybe this is a clue about the private key
    
    test_strings = [
        "even",
        "odd",
        "evenodd",
        "oddeven",
        "Nintondoeven",
        "Nintondoodd",
        "Nintondoevenodd",
        "Nintondooddeven",
        "firsteven",
        "firstodd",
        "secondeven",
        "secondodd",
        "Nintondofirsteven",
        "Nintondofirstodd",
        "Nintondosecondeven",
        "Nintondosecondodd",
        "1beven",
        "1codd",
        "1deven",
        "1eodd",
        "Nintondo1beven",
        "Nintondo1codd", 
        "Nintondo1deven",
        "Nintondo1eodd",
    ]
    
    for test_str in test_strings:
        hash_result = sha256_hash(test_str)
        if test_private_key(hash_result):
            print(f"🎯 TREASURE FOUND! Even/odd clue: {test_str}")
            return hash_result
    
    return None

def main():
    """Main execution function"""
    print("🏴‍☠️ SIGNATURE HEADER ANALYSIS")
    print("Analyzing signature header bytes as clues to the private key...")
    print(f"Target: {TARGET_PUBKEY[:32]}...")
    
    # Test different analysis approaches
    result = analyze_signature_headers()
    if result:
        print(f"\n🏆 TREASURE DISCOVERED: {result}")
        return
    
    result = analyze_header_combinations()
    if result:
        print(f"\n🏆 TREASURE DISCOVERED: {result}")
        return
    
    result = analyze_header_in_constants()
    if result:
        print(f"\n🏆 TREASURE DISCOVERED: {result}")
        return
    
    result = analyze_signature_recovery_clue()
    if result:
        print(f"\n🏆 TREASURE DISCOVERED: {result}")
        return
    
    result = analyze_even_odd_clue()
    if result:
        print(f"\n🏆 TREASURE DISCOVERED: {result}")
        return
    
    print("\n💔 No treasure found in signature header analysis")
    print("🔍 The private key might require a different interpretation...")

if __name__ == "__main__":
    main()
