#!/usr/bin/env python3
"""
Analyze nBits Clue
==================

The genesis block has nBits = 0x1e0ffff0, which is unusual.
Let's analyze this value and see if it contains clues to the private key.

Also analyzing other unique values:
- Message start: 0xc0c0c0c0
- nBits: 0x1e0ffff0
- Script constant: 486604799 (0x1d00ffff)
"""

import hashlib
from ecdsa import SigningKey, SECP256k1
from ecdsa.util import string_to_number, number_to_string

# Target genesis public key
TARGET_PUBKEY = "040184710fa689ad5023690c80f3a49c8f13f8d45b8c857fbcbc8bc4a8e4d3eb4b10f4d4604fa08dce601aaf0f470216fe1b51850b4acf21b179c45070ac7b03a9"

# Key values from genesis block
NBITS = 0x1e0ffff0
MESSAGE_START = 0xc0c0c0c0
SCRIPT_CONSTANT = 486604799  # 0x1d00ffff

def test_private_key(private_key_hex: str) -> bool:
    """Test if a private key generates the target public key"""
    try:
        if len(private_key_hex) != 64:
            private_key_hex = private_key_hex.zfill(64)
        
        private_key_int = int(private_key_hex, 16)
        if private_key_int == 0 or private_key_int >= SECP256k1.order:
            return False
        
        private_key_bytes = number_to_string(private_key_int, SECP256k1.order)
        signing_key = SigningKey.from_string(private_key_bytes, curve=SECP256k1)
        verifying_key = signing_key.get_verifying_key()
        
        uncompressed_pubkey = "04" + verifying_key.to_string().hex()
        return uncompressed_pubkey.lower() == TARGET_PUBKEY.lower()
    except:
        return False

def sha256_hash(data: str) -> str:
    """Generate SHA256 hash of string"""
    return hashlib.sha256(data.encode()).hexdigest()

def analyze_nbits():
    """Analyze the nBits value for clues"""
    print("🔍 ANALYZING NBITS VALUE")
    print("=" * 50)
    
    print(f"nBits: 0x{NBITS:08x} = {NBITS}")
    print(f"Binary: {bin(NBITS)}")
    
    # Break down the nBits value
    # In Bitcoin, nBits is a compact representation of the target
    # Format: 0x1e0ffff0
    exponent = (NBITS >> 24) & 0xff  # 0x1e = 30
    mantissa = NBITS & 0x00ffffff    # 0x0ffff0
    
    print(f"Exponent: 0x{exponent:02x} = {exponent}")
    print(f"Mantissa: 0x{mantissa:06x} = {mantissa}")
    
    # Test various transformations
    test_values = [
        (str(NBITS), "decimal"),
        (f"{NBITS:08x}", "hex"),
        (f"{NBITS:032b}", "binary"),
        (str(exponent), "exponent"),
        (str(mantissa), "mantissa"),
        (f"{exponent:02x}", "exponent hex"),
        (f"{mantissa:06x}", "mantissa hex"),
        (str(exponent) + str(mantissa), "exponent + mantissa"),
        (f"{exponent:02x}{mantissa:06x}", "combined hex"),
    ]
    
    for value, description in test_values:
        print(f"\nTesting {description}: {value}")
        
        # Test direct hash
        hash_result = sha256_hash(value)
        if test_private_key(hash_result):
            print(f"🎯 TREASURE FOUND! nBits {description}: {value}")
            return hash_result
        
        # Test with Nintondo prefix
        nintondo_value = "Nintondo" + value
        hash_result = sha256_hash(nintondo_value)
        if test_private_key(hash_result):
            print(f"🎯 TREASURE FOUND! Nintondo + {description}: {nintondo_value}")
            return hash_result
    
    return None

def analyze_message_start():
    """Analyze the message start bytes"""
    print(f"\n🔍 ANALYZING MESSAGE START BYTES")
    print("=" * 50)
    
    print(f"Message start: 0x{MESSAGE_START:08x} = {MESSAGE_START}")
    print(f"Binary: {bin(MESSAGE_START)}")
    
    # The value 0xc0c0c0c0 is interesting - it's a repeating pattern
    # Each byte is 0xc0 = 192 = 11000000 in binary
    
    test_values = [
        (str(MESSAGE_START), "decimal"),
        (f"{MESSAGE_START:08x}", "hex"),
        ("c0c0c0c0", "hex string"),
        ("192192192192", "decimal bytes"),
        ("11000000110000001100000011000000", "binary"),
    ]
    
    for value, description in test_values:
        print(f"\nTesting {description}: {value}")
        
        # Test direct hash
        hash_result = sha256_hash(value)
        if test_private_key(hash_result):
            print(f"🎯 TREASURE FOUND! Message start {description}: {value}")
            return hash_result
        
        # Test with Nintondo prefix
        nintondo_value = "Nintondo" + value
        hash_result = sha256_hash(nintondo_value)
        if test_private_key(hash_result):
            print(f"🎯 TREASURE FOUND! Nintondo + {description}: {nintondo_value}")
            return hash_result
    
    return None

def analyze_combined_values():
    """Analyze combinations of the special values"""
    print(f"\n🔍 ANALYZING COMBINED VALUES")
    print("=" * 50)
    
    # Combine the special hex values
    combinations = [
        (f"{NBITS:08x}{MESSAGE_START:08x}", "nBits + MessageStart"),
        (f"{MESSAGE_START:08x}{NBITS:08x}", "MessageStart + nBits"),
        (f"{SCRIPT_CONSTANT:08x}{NBITS:08x}", "ScriptConst + nBits"),
        (f"{NBITS:08x}{SCRIPT_CONSTANT:08x}", "nBits + ScriptConst"),
        (f"{MESSAGE_START:08x}{SCRIPT_CONSTANT:08x}", "MessageStart + ScriptConst"),
        (f"{SCRIPT_CONSTANT:08x}{MESSAGE_START:08x}", "ScriptConst + MessageStart"),
        
        # All three combined
        (f"{NBITS:08x}{MESSAGE_START:08x}{SCRIPT_CONSTANT:08x}", "All three"),
        (f"{MESSAGE_START:08x}{NBITS:08x}{SCRIPT_CONSTANT:08x}", "Reordered 1"),
        (f"{SCRIPT_CONSTANT:08x}{NBITS:08x}{MESSAGE_START:08x}", "Reordered 2"),
    ]
    
    for value, description in combinations:
        print(f"\nTesting {description}: {value}")
        
        # Test direct hash
        hash_result = sha256_hash(value)
        if test_private_key(hash_result):
            print(f"🎯 TREASURE FOUND! Combined {description}: {value}")
            return hash_result
        
        # Test with Nintondo prefix
        nintondo_value = "Nintondo" + value
        hash_result = sha256_hash(nintondo_value)
        if test_private_key(hash_result):
            print(f"🎯 TREASURE FOUND! Nintondo + {description}: {nintondo_value}")
            return hash_result
        
        # Test as direct private key if 64 chars
        if len(value) == 64:
            if test_private_key(value):
                print(f"🎯 TREASURE FOUND! Direct key {description}: {value}")
                return value
    
    return None

def analyze_bit_patterns():
    """Analyze interesting bit patterns in the values"""
    print(f"\n🔍 ANALYZING BIT PATTERNS")
    print("=" * 50)
    
    # Look for XOR patterns
    xor_results = [
        (NBITS ^ MESSAGE_START, "nBits XOR MessageStart"),
        (NBITS ^ SCRIPT_CONSTANT, "nBits XOR ScriptConst"),
        (MESSAGE_START ^ SCRIPT_CONSTANT, "MessageStart XOR ScriptConst"),
        (NBITS ^ MESSAGE_START ^ SCRIPT_CONSTANT, "All three XOR"),
    ]
    
    for result, description in xor_results:
        print(f"\nTesting {description}: 0x{result:08x} = {result}")
        
        # Test various representations
        test_values = [
            str(result),
            f"{result:08x}",
            f"{result:032b}",
        ]
        
        for value in test_values:
            hash_result = sha256_hash(value)
            if test_private_key(hash_result):
                print(f"🎯 TREASURE FOUND! XOR {description}: {value}")
                return hash_result
            
            # Test with Nintondo
            nintondo_value = "Nintondo" + value
            hash_result = sha256_hash(nintondo_value)
            if test_private_key(hash_result):
                print(f"🎯 TREASURE FOUND! Nintondo + XOR {description}: {nintondo_value}")
                return hash_result
    
    return None

def main():
    """Main execution function"""
    print("🏴‍☠️ NBITS AND SPECIAL VALUES ANALYSIS")
    print("Analyzing special values from the genesis block...")
    print(f"Target: {TARGET_PUBKEY[:32]}...")
    
    # Test different analysis approaches
    result = analyze_nbits()
    if result:
        print(f"\n🏆 TREASURE DISCOVERED: {result}")
        return
    
    result = analyze_message_start()
    if result:
        print(f"\n🏆 TREASURE DISCOVERED: {result}")
        return
    
    result = analyze_combined_values()
    if result:
        print(f"\n🏆 TREASURE DISCOVERED: {result}")
        return
    
    result = analyze_bit_patterns()
    if result:
        print(f"\n🏆 TREASURE DISCOVERED: {result}")
        return
    
    print("\n💔 No treasure found in nBits analysis")
    print("🔍 The private key might be hidden in a different pattern...")

if __name__ == "__main__":
    main()
