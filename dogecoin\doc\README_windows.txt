Litecoin 0.6 BETA

Copyright (c) 2009-2012 Bitcoin Developers
Copyright (c) 2011-2012 Litecoin Developers
Distributed under the MIT/X11 software license, see the accompanying
file COPYING or http://www.opensource.org/licenses/mit-license.php.
This product includes software developed by the OpenSSL Project for use in
the OpenSSL Toolkit (http://www.openssl.org/).  This product includes
cryptographic software written by <PERSON> (<EMAIL>).


Intro
-----
Litecoin is a free open source peer-to-peer electronic cash system that is
completely decentralized, without the need for a central server or trusted
parties.  Users hold the crypto keys to their own money and transact directly
with each other, with the help of a P2P network to check for double-spending.


Setup
-----
Unpack the files into a directory and run litecoin-qt.exe.

If you have Microsoft Security Essentials, you need to add litecoin-qt.exe to its
"Excluded processes" list.  Microsoft Security Essentials->Settings tab,
select Excluded processes, press Add, select litecoin-qt.exe, OK, Save changes.

The software automatically finds other nodes to connect to.  You can
enable Universal Plug and Play using a menu entry or set your firewall
to forward port 9333 (TCP) to your computer so you can receive
incoming connections.  Litecoin works without incoming connections,
but allowing incoming connections helps the Litecoin network.

See the bitcoin wiki at:
  https://en.bitcoin.it/wiki/Main_Page
for more help and information.
