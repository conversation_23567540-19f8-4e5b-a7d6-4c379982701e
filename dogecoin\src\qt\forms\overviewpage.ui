<?xml version="1.0" encoding="UTF-8"?>
<ui version="4.0">
 <class>OverviewPage</class>
 <widget class="QWidget" name="OverviewPage">
  <property name="geometry">
   <rect>
    <x>0</x>
    <y>0</y>
    <width>573</width>
    <height>342</height>
   </rect>
  </property>
  <property name="windowTitle">
   <string>Form</string>
  </property>
  <layout class="QHBoxLayout" name="horizontalLayout" stretch="1,1">
   <item>
    <layout class="QVBoxLayout" name="verticalLayout_2">
     <item>
      <widget class="QFrame" name="frame">
       <property name="frameShape">
        <enum>QFrame::StyledPanel</enum>
       </property>
       <property name="frameShadow">
        <enum>QFrame::Raised</enum>
       </property>
       <layout class="QVBoxLayout" name="verticalLayout_4">
        <item>
         <layout class="QHBoxLayout" name="horizontalLayout_4">
          <item>
           <widget class="QLabel" name="label_5">
            <property name="font">
             <font>
              <pointsize>11</pointsize>
              <weight>75</weight>
              <bold>true</bold>
             </font>
            </property>
            <property name="text">
             <string>Wallet</string>
            </property>
           </widget>
          </item>
          <item>
           <widget class="QLabel" name="labelWalletStatus">
            <property name="toolTip">
             <string>The displayed information may be out of date. Your wallet automatically synchronizes with the DogeCoin network after a connection is established, but this process has not completed yet.</string>
            </property>
            <property name="styleSheet">
             <string notr="true">QLabel { color: red; }</string>
            </property>
            <property name="text">
             <string notr="true">(out of sync)</string>
            </property>
            <property name="alignment">
             <set>Qt::AlignLeading|Qt::AlignLeft|Qt::AlignVCenter</set>
            </property>
           </widget>
          </item>
          <item>
           <spacer name="horizontalSpacer_2">
            <property name="orientation">
             <enum>Qt::Horizontal</enum>
            </property>
            <property name="sizeHint" stdset="0">
             <size>
              <width>40</width>
              <height>20</height>
             </size>
            </property>
           </spacer>
          </item>
         </layout>
        </item>
        <item>
         <layout class="QFormLayout" name="formLayout_2">
          <property name="fieldGrowthPolicy">
           <enum>QFormLayout::AllNonFixedFieldsGrow</enum>
          </property>
          <property name="horizontalSpacing">
           <number>12</number>
          </property>
          <property name="verticalSpacing">
           <number>12</number>
          </property>
          <item row="0" column="0">
           <widget class="QLabel" name="label">
            <property name="text">
             <string>Balance:</string>
            </property>
           </widget>
          </item>
          <item row="0" column="1">
           <widget class="QLabel" name="labelBalance">
            <property name="font">
             <font>
              <weight>75</weight>
              <bold>true</bold>
             </font>
            </property>
            <property name="cursor">
             <cursorShape>IBeamCursor</cursorShape>
            </property>
            <property name="toolTip">
             <string>Your current balance</string>
            </property>
            <property name="text">
             <string notr="true">0 BEL</string>
            </property>
            <property name="textInteractionFlags">
             <set>Qt::LinksAccessibleByMouse|Qt::TextSelectableByKeyboard|Qt::TextSelectableByMouse</set>
            </property>
           </widget>
          </item>
          <item row="1" column="0">
           <widget class="QLabel" name="label_3">
            <property name="text">
             <string>Unconfirmed:</string>
            </property>
           </widget>
          </item>
          <item row="1" column="1">
           <widget class="QLabel" name="labelUnconfirmed">
            <property name="font">
             <font>
              <weight>75</weight>
              <bold>true</bold>
             </font>
            </property>
            <property name="cursor">
             <cursorShape>IBeamCursor</cursorShape>
            </property>
            <property name="toolTip">
             <string>Total of transactions that have yet to be confirmed, and do not yet count toward the current balance</string>
            </property>
            <property name="text">
             <string notr="true">0 BEL</string>
            </property>
            <property name="textInteractionFlags">
             <set>Qt::LinksAccessibleByMouse|Qt::TextSelectableByKeyboard|Qt::TextSelectableByMouse</set>
            </property>
           </widget>
          </item>
          <item row="3" column="0">
           <widget class="QLabel" name="label_2">
            <property name="text">
             <string>Number of transactions:</string>
            </property>
           </widget>
          </item>
          <item row="3" column="1">
           <widget class="QLabel" name="labelNumTransactions">
            <property name="toolTip">
             <string>Total number of transactions in wallet</string>
            </property>
            <property name="text">
             <string notr="true">0</string>
            </property>
           </widget>
          </item>
          <item row="2" column="0">
           <widget class="QLabel" name="labelImmatureText">
            <property name="text">
             <string>Immature:</string>
            </property>
           </widget>
          </item>
          <item row="2" column="1">
           <widget class="QLabel" name="labelImmature">
            <property name="font">
             <font>
              <weight>75</weight>
              <bold>true</bold>
             </font>
            </property>
            <property name="toolTip">
             <string>Mined balance that has not yet matured</string>
            </property>
            <property name="text">
             <string notr="true">0 BEL</string>
            </property>
            <property name="textInteractionFlags">
             <set>Qt::LinksAccessibleByMouse|Qt::TextSelectableByKeyboard|Qt::TextSelectableByMouse</set>
            </property>
           </widget>
          </item>
         </layout>
        </item>
       </layout>
      </widget>
     </item>
     <item>
      <spacer name="verticalSpacer">
       <property name="orientation">
        <enum>Qt::Vertical</enum>
       </property>
       <property name="sizeHint" stdset="0">
        <size>
         <width>20</width>
         <height>40</height>
        </size>
       </property>
      </spacer>
     </item>
	 <item>
         <widget class="QLabel" name="label_wallet_bgcoin">
          <property name="text">
           <string/>
          </property>
          <property name="pixmap">
           <pixmap resource="../bitcoin.qrc">:/images/wallet_bgcoin</pixmap>
          </property>
          <property name="scaledContents">
           <bool>false</bool>
          </property>
          <property name="alignment">
           <set>Qt::AlignCenter</set>
          </property>
          <property name="margin">
           <number>-2</number>
          </property>
         </widget>
        </item>
        <item>
         <widget class="QLabel" name="label_6">
          <property name="text">
           <string/>
          </property>
         </widget>
        </item>
    </layout>
   </item>
   <item>
    <layout class="QVBoxLayout" name="verticalLayout_3">
     <item>
      <widget class="QFrame" name="frame_2">
       <property name="frameShape">
        <enum>QFrame::StyledPanel</enum>
       </property>
       <property name="frameShadow">
        <enum>QFrame::Raised</enum>
       </property>
       <layout class="QVBoxLayout" name="verticalLayout">
        <item>
         <layout class="QHBoxLayout" name="horizontalLayout_2">
          <item>
           <widget class="QLabel" name="label_4">
            <property name="text">
             <string>&lt;b&gt;Recent transactions&lt;/b&gt;</string>
            </property>
           </widget>
          </item>
          <item>
           <widget class="QLabel" name="labelTransactionsStatus">
            <property name="toolTip">
             <string>The displayed information may be out of date. Your wallet automatically synchronizes with the DogeCoin network after a connection is established, but this process has not completed yet.</string>
            </property>
            <property name="styleSheet">
             <string notr="true">QLabel { color: red; }</string>
            </property>
            <property name="text">
             <string notr="true">(out of sync)</string>
            </property>
            <property name="alignment">
             <set>Qt::AlignRight|Qt::AlignTrailing|Qt::AlignVCenter</set>
            </property>
           </widget>
          </item>
          <item>
           <spacer name="horizontalSpacer">
            <property name="orientation">
             <enum>Qt::Horizontal</enum>
            </property>
            <property name="sizeHint" stdset="0">
             <size>
              <width>40</width>
              <height>20</height>
             </size>
            </property>
           </spacer>
          </item>
         </layout>
        </item>
        <item>
         <widget class="QListView" name="listTransactions">
          <property name="styleSheet">
           <string notr="true">QListView { background: transparent; }</string>
          </property>
          <property name="frameShape">
           <enum>QFrame::NoFrame</enum>
          </property>
          <property name="verticalScrollBarPolicy">
           <enum>Qt::ScrollBarAlwaysOff</enum>
          </property>
          <property name="horizontalScrollBarPolicy">
           <enum>Qt::ScrollBarAlwaysOff</enum>
          </property>
          <property name="selectionMode">
           <enum>QAbstractItemView::NoSelection</enum>
          </property>
         </widget>
        </item>
       </layout>
      </widget>
     </item>
     <item>
      <spacer name="verticalSpacer_2">
       <property name="orientation">
        <enum>Qt::Vertical</enum>
       </property>
       <property name="sizeHint" stdset="0">
        <size>
         <width>20</width>
         <height>40</height>
        </size>
       </property>
      </spacer>
     </item>
    </layout>
   </item>
  </layout>
 </widget>
 <resources/>
 <connections/>
</ui>
