<?xml version="1.0" encoding="UTF-8"?>
<ui version="4.0">
 <class>SendCoinsEntry</class>
 <widget class="QFrame" name="SendCoinsEntry">
  <property name="geometry">
   <rect>
    <x>0</x>
    <y>0</y>
    <width>729</width>
    <height>136</height>
   </rect>
  </property>
  <property name="windowTitle">
   <string>Form</string>
  </property>
  <property name="frameShape">
   <enum>QFrame::StyledPanel</enum>
  </property>
  <property name="frameShadow">
   <enum>QFrame::Sunken</enum>
  </property>
  <layout class="QGridLayout" name="gridLayout">
   <property name="spacing">
    <number>12</number>
   </property>
   <item row="5" column="0">
    <widget class="QLabel" name="label">
     <property name="text">
      <string>A&amp;mount:</string>
     </property>
     <property name="alignment">
      <set>Qt::AlignRight|Qt::AlignTrailing|Qt::AlignVCenter</set>
     </property>
     <property name="buddy">
      <cstring>payAmount</cstring>
     </property>
    </widget>
   </item>
   <item row="3" column="0">
    <widget class="QLabel" name="label_2">
     <property name="text">
      <string>Pay &amp;To:</string>
     </property>
     <property name="alignment">
      <set>Qt::AlignRight|Qt::AlignTrailing|Qt::AlignVCenter</set>
     </property>
     <property name="buddy">
      <cstring>payTo</cstring>
     </property>
    </widget>
   </item>
   <item row="5" column="1">
    <widget class="BitcoinAmountField" name="payAmount"/>
   </item>
   <item row="4" column="1">
    <layout class="QHBoxLayout" name="horizontalLayout_2">
     <property name="spacing">
      <number>0</number>
     </property>
     <item>
      <widget class="QValidatedLineEdit" name="addAsLabel">
       <property name="enabled">
        <bool>true</bool>
       </property>
       <property name="toolTip">
        <string>Enter a label for this address to add it to your address book</string>
       </property>
      </widget>
     </item>
    </layout>
   </item>
   <item row="4" column="0">
    <widget class="QLabel" name="label_4">
     <property name="text">
      <string>&amp;Label:</string>
     </property>
     <property name="alignment">
      <set>Qt::AlignRight|Qt::AlignTrailing|Qt::AlignVCenter</set>
     </property>
     <property name="buddy">
      <cstring>addAsLabel</cstring>
     </property>
    </widget>
   </item>
   <item row="3" column="1">
    <layout class="QHBoxLayout" name="payToLayout">
     <property name="spacing">
      <number>0</number>
     </property>
     <item>
      <widget class="QValidatedLineEdit" name="payTo">
       <property name="toolTip">
        <string>The address to send the payment to  (e.g. **********************************)</string>
       </property>
       <property name="maxLength">
        <number>34</number>
       </property>
      </widget>
     </item>
     <item>
      <widget class="QToolButton" name="addressBookButton">
       <property name="toolTip">
        <string>Choose address from address book</string>
       </property>
       <property name="text">
        <string/>
       </property>
       <property name="icon">
        <iconset resource="../bitcoin.qrc">
         <normaloff>:/icons/address-book</normaloff>:/icons/address-book</iconset>
       </property>
       <property name="shortcut">
        <string>Alt+A</string>
       </property>
      </widget>
     </item>
     <item>
      <widget class="QToolButton" name="pasteButton">
       <property name="toolTip">
        <string>Paste address from clipboard</string>
       </property>
       <property name="text">
        <string/>
       </property>
       <property name="icon">
        <iconset resource="../bitcoin.qrc">
         <normaloff>:/icons/editpaste</normaloff>:/icons/editpaste</iconset>
       </property>
       <property name="shortcut">
        <string>Alt+P</string>
       </property>
      </widget>
     </item>
     <item>
      <widget class="QToolButton" name="deleteButton">
       <property name="toolTip">
        <string>Remove this recipient</string>
       </property>
       <property name="text">
        <string/>
       </property>
       <property name="icon">
        <iconset resource="../bitcoin.qrc">
         <normaloff>:/icons/remove</normaloff>:/icons/remove</iconset>
       </property>
      </widget>
     </item>
    </layout>
   </item>
  </layout>
 </widget>
 <customwidgets>
  <customwidget>
   <class>BitcoinAmountField</class>
   <extends>QLineEdit</extends>
   <header>bitcoinamountfield.h</header>
   <container>1</container>
  </customwidget>
  <customwidget>
   <class>QValidatedLineEdit</class>
   <extends>QLineEdit</extends>
   <header>qvalidatedlineedit.h</header>
  </customwidget>
 </customwidgets>
 <resources>
  <include location="../bitcoin.qrc"/>
 </resources>
 <connections/>
</ui>
