<?xml version="1.0" encoding="utf-8"?>
<!DOCTYPE TS>
<TS version="2.0" language="en">
<defaultcodec>UTF-8</defaultcodec>
<context>
    <name>AboutDialog</name>
    <message>
        <source>About Bitcoin</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <source>&lt;b&gt;Bitcoin&lt;/b&gt; version</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <source>Copyright © 2009-2012 Bitcoin Developers

This is experimental software.

Distributed under the MIT/X11 software license, see the accompanying file license.txt or http://www.opensource.org/licenses/mit-license.php.

This product includes software developed by the OpenSSL Project for use in the OpenSSL Toolkit (http://www.openssl.org/) and cryptographic software written by <PERSON> (<EMAIL>) and UPnP software written by <PERSON>.</source>
        <translation type="unfinished"></translation>
    </message>
</context>
<context>
    <name>AddressBookPage</name>
    <message>
        <source>Address Book</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <source>Double-click to edit address or label</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <source>Create a new address</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <source>Copy the currently selected address to the system clipboard</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <source>&amp;New Address</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <source>These are your Bitcoin addresses for receiving payments. You may want to give a different one to each sender so you can keep track of who is paying you.</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <source>&amp;Copy Address</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <source>Show &amp;QR Code</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <source>Sign a message to prove you own a Bitcoin address</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <source>&amp;Sign Message</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <source>Verify a message to ensure it was signed with a specified Bitcoin address</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <source>&amp;Verify Message</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <source>Delete the currently selected address from the list. Only sending addresses can be deleted.</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <source>&amp;Delete</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <source>Copy &amp;Label</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <source>&amp;Edit</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <source>Export Address Book Data</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <source>Comma separated file (*.csv)</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <source>Error exporting</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <source>Could not write to file %1.</source>
        <translation type="unfinished"></translation>
    </message>
</context>
<context>
    <name>AddressTableModel</name>
    <message>
        <source>Label</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <source>Address</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <source>(no label)</source>
        <translation type="unfinished"></translation>
    </message>
</context>
<context>
    <name>AskPassphraseDialog</name>
    <message>
        <source>Passphrase Dialog</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <source>Enter passphrase</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <source>New passphrase</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <source>Repeat new passphrase</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <source>Enter the new passphrase to the wallet.&lt;br/&gt;Please use a passphrase of &lt;b&gt;10 or more random characters&lt;/b&gt;, or &lt;b&gt;eight or more words&lt;/b&gt;.</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <source>Encrypt wallet</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <source>This operation needs your wallet passphrase to unlock the wallet.</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <source>Unlock wallet</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <source>This operation needs your wallet passphrase to decrypt the wallet.</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <source>Decrypt wallet</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <source>Change passphrase</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <source>Enter the old and new passphrase to the wallet.</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <source>Confirm wallet encryption</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <source>WARNING: If you encrypt your wallet and lose your passphrase, you will &lt;b&gt;LOSE ALL OF YOUR BITCOINS&lt;/b&gt;!
Are you sure you wish to encrypt your wallet?</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <source>Wallet encrypted</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <source>Bitcoin will close now to finish the encryption process. Remember that encrypting your wallet cannot fully protect your bitcoins from being stolen by malware infecting your computer.</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <source>Warning: The Caps Lock key is on.</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <source>Wallet encryption failed</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <source>Wallet encryption failed due to an internal error. Your wallet was not encrypted.</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <source>The supplied passphrases do not match.</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <source>Wallet unlock failed</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <source>The passphrase entered for the wallet decryption was incorrect.</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <source>Wallet decryption failed</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <source>Wallet passphrase was successfully changed.</source>
        <translation type="unfinished"></translation>
    </message>
</context>
<context>
    <name>BitcoinGUI</name>
    <message>
        <source>Bitcoin Wallet</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <source>Sign &amp;message...</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <source>Show/Hide &amp;Bitcoin</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <source>Synchronizing with network...</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <source>&amp;Overview</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <source>Show general overview of wallet</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <source>&amp;Transactions</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <source>Browse transaction history</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <source>&amp;Address Book</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <source>Edit the list of stored addresses and labels</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <source>&amp;Receive coins</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <source>Show the list of addresses for receiving payments</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <source>&amp;Send coins</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <source>E&amp;xit</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <source>Quit application</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <source>&amp;About %1</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <source>Show information about Bitcoin</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <source>About &amp;Qt</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <source>Show information about Qt</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <source>&amp;Options...</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <source>&amp;Encrypt Wallet...</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <source>&amp;Backup Wallet...</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <source>&amp;Change Passphrase...</source>
        <translation type="unfinished"></translation>
    </message>
    <message numerus="yes">
        <source>~%n block(s) remaining</source>
        <translation>
            <numerusform>~%n block remaining</numerusform>
            <numerusform>~%n blocks remaining</numerusform>
        </translation>
    </message>
    <message>
        <source>Downloaded %1 of %2 blocks of transaction history (%3% done).</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <source>&amp;Export...</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <source>Send coins to a Bitcoin address</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <source>Sign a message to prove you own a Bitcoin address</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <source>Verify a message to ensure it was signed with a specified Bitcoin address</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <source>S&amp;ignatures</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <source>Modify configuration options for Bitcoin</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <source>Show or hide the Bitcoin window</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <source>Export the data in the current tab to a file</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <source>Encrypt or decrypt wallet</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <source>Backup wallet to another location</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <source>Change the passphrase used for wallet encryption</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <source>&amp;Debug window</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <source>Open debugging and diagnostic console</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <source>&amp;Verify message...</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <source>&amp;File</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <source>&amp;Settings</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <source>&amp;Help</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <source>Tabs toolbar</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <source>Actions toolbar</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <source>[testnet]</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <source>Bitcoin client</source>
        <translation type="unfinished"></translation>
    </message>
    <message numerus="yes">
        <source>%n active connection(s) to Bitcoin network</source>
        <translation>
            <numerusform>%n active connection to Bitcoin network</numerusform>
            <numerusform>%n active connections to Bitcoin network</numerusform>
        </translation>
    </message>
    <message>
        <source>Downloaded %1 blocks of transaction history.</source>
        <translation type="unfinished"></translation>
    </message>
    <message numerus="yes">
        <source>%n second(s) ago</source>
        <translation>
            <numerusform>%n second ago</numerusform>
            <numerusform>%n seconds ago</numerusform>
        </translation>
    </message>
    <message numerus="yes">
        <source>%n minute(s) ago</source>
        <translation>
            <numerusform>%n minute ago</numerusform>
            <numerusform>%n minutes ago</numerusform>
        </translation>
    </message>
    <message numerus="yes">
        <source>%n hour(s) ago</source>
        <translation>
            <numerusform>%n hour ago</numerusform>
            <numerusform>%n hours ago</numerusform>
        </translation>
    </message>
    <message numerus="yes">
        <source>%n day(s) ago</source>
        <translation>
            <numerusform>%n day ago</numerusform>
            <numerusform>%n days ago</numerusform>
        </translation>
    </message>
    <message>
        <source>Up to date</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <source>Catching up...</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <source>Last received block was generated %1.</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <source>This transaction is over the size limit.  You can still send it for a fee of %1, which goes to the nodes that process your transaction and helps to support the network.  Do you want to pay the fee?</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <source>Confirm transaction fee</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <source>Sent transaction</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <source>Incoming transaction</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <source>Date: %1
Amount: %2
Type: %3
Address: %4
</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <source>URI handling</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <source>URI can not be parsed! This can be caused by an invalid Bitcoin address or malformed URI parameters.</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <source>Wallet is &lt;b&gt;encrypted&lt;/b&gt; and currently &lt;b&gt;unlocked&lt;/b&gt;</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <source>Wallet is &lt;b&gt;encrypted&lt;/b&gt; and currently &lt;b&gt;locked&lt;/b&gt;</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <source>Backup Wallet</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <source>Wallet Data (*.dat)</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <source>Backup Failed</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <source>There was an error trying to save the wallet data to the new location.</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <source>A fatal error occured. Bitcoin can no longer continue safely and will quit.</source>
        <translation type="unfinished"></translation>
    </message>
</context>
<context>
    <name>ClientModel</name>
    <message>
        <source>Network Alert</source>
        <translation type="unfinished"></translation>
    </message>
</context>
<context>
    <name>EditAddressDialog</name>
    <message>
        <source>Edit Address</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <source>&amp;Label</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <source>The label associated with this address book entry</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <source>&amp;Address</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <source>The address associated with this address book entry. This can only be modified for sending addresses.</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <source>New receiving address</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <source>New sending address</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <source>Edit receiving address</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <source>Edit sending address</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <source>The entered address &quot;%1&quot; is already in the address book.</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <source>The entered address &quot;%1&quot; is not a valid Bitcoin address.</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <source>Could not unlock wallet.</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <source>New key generation failed.</source>
        <translation type="unfinished"></translation>
    </message>
</context>
<context>
    <name>GUIUtil::HelpMessageBox</name>
    <message>
        <source>Bitcoin-Qt</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <source>version</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <source>Usage:</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <source>command-line options</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <source>UI options</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <source>Set language, for example &quot;de_DE&quot; (default: system locale)</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <source>Start minimized</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <source>Show splash screen on startup (default: 1)</source>
        <translation type="unfinished"></translation>
    </message>
</context>
<context>
    <name>OptionsDialog</name>
    <message>
        <source>Options</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <source>&amp;Main</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <source>Optional transaction fee per kB that helps make sure your transactions are processed quickly. Most transactions are 1 kB. Fee 0.01 recommended.</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <source>Pay transaction &amp;fee</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <source>Automatically start Bitcoin after logging in to the system.</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <source>&amp;Start Bitcoin on system login</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <source>Detach block and address databases at shutdown. This means they can be moved to another data directory, but it slows down shutdown. The wallet is always detached.</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <source>&amp;Detach databases at shutdown</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <source>&amp;Network</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <source>Automatically open the Bitcoin client port on the router. This only works when your router supports UPnP and it is enabled.</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <source>Map port using &amp;UPnP</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <source>Connect to the Bitcoin network through a SOCKS proxy (e.g. when connecting through Tor).</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <source>&amp;Connect through SOCKS proxy:</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <source>Proxy &amp;IP:</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <source>IP address of the proxy (e.g. 127.0.0.1)</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <source>&amp;Port:</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <source>Port of the proxy (e.g. 9050)</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <source>SOCKS &amp;Version:</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <source>SOCKS version of the proxy (e.g. 5)</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <source>&amp;Window</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <source>Show only a tray icon after minimizing the window.</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <source>&amp;Minimize to the tray instead of the taskbar</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <source>Minimize instead of exit the application when the window is closed. When this option is enabled, the application will be closed only after selecting Quit in the menu.</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <source>M&amp;inimize on close</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <source>&amp;Display</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <source>User Interface &amp;language:</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <source>The user interface language can be set here. This setting will take effect after restarting Bitcoin.</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <source>&amp;Unit to show amounts in:</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <source>Choose the default subdivision unit to show in the interface and when sending coins.</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <source>Whether to show Bitcoin addresses in the transaction list or not.</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <source>&amp;Display addresses in transaction list</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <source>&amp;OK</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <source>&amp;Cancel</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <source>&amp;Apply</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <source>default</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <source>Warning</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <source>This setting will take effect after restarting Bitcoin.</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <source>The supplied proxy address is invalid.</source>
        <translation type="unfinished"></translation>
    </message>
</context>
<context>
    <name>OverviewPage</name>
    <message>
        <source>Form</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <source>The displayed information may be out of date. Your wallet automatically synchronizes with the Bitcoin network after a connection is established, but this process has not completed yet.</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <source>Balance:</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <source>Number of transactions:</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <source>Unconfirmed:</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <source>Wallet</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <source>Immature:</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <source>Mined balance that has not yet matured</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <source>&lt;b&gt;Recent transactions&lt;/b&gt;</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <source>Your current balance</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <source>Total of transactions that have yet to be confirmed, and do not yet count toward the current balance</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <source>Total number of transactions in wallet</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <source>out of sync</source>
        <translation type="unfinished"></translation>
    </message>
</context>
<context>
    <name>QRCodeDialog</name>
    <message>
        <source>QR Code Dialog</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <source>Request Payment</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <source>Amount:</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <source>Label:</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <source>Message:</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <source>&amp;Save As...</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <source>Error encoding URI into QR Code.</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <source>The entered amount is invalid, please check.</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <source>Resulting URI too long, try to reduce the text for label / message.</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <source>Save QR Code</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <source>PNG Images (*.png)</source>
        <translation type="unfinished"></translation>
    </message>
</context>
<context>
    <name>RPCConsole</name>
    <message>
        <source>Bitcoin debug window</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <source>Client name</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <source>N/A</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <source>Client version</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <source>&amp;Information</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <source>Client</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <source>Using OpenSSL version</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <source>Startup time</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <source>Network</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <source>Number of connections</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <source>On testnet</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <source>Block chain</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <source>Current number of blocks</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <source>Estimated total blocks</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <source>Last block time</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <source>Debug logfile</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <source>Open the Bitcoin debug logfile from the current data directory. This can take a few seconds for large logfiles.</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <source>&amp;Open</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <source>Command-line options</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <source>Show the Bitcoin-Qt help message to get a list with possible Bitcoin command-line options.</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <source>&amp;Show</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <source>&amp;Console</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <source>Build date</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <source>Clear console</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <source>Welcome to the Bitcoin RPC console.</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <source>Use up and down arrows to navigate history, and &lt;b&gt;Ctrl-L&lt;/b&gt; to clear screen.</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <source>Type &lt;b&gt;help&lt;/b&gt; for an overview of available commands.</source>
        <translation type="unfinished"></translation>
    </message>
</context>
<context>
    <name>SendCoinsDialog</name>
    <message>
        <source>Send Coins</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <source>Send to multiple recipients at once</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <source>&amp;Add Recipient</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <source>Remove all transaction fields</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <source>Clear &amp;All</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <source>Balance:</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <source>123.456 BTC</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <source>Confirm the send action</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <source>&amp;Send</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <source>&lt;b&gt;%1&lt;/b&gt; to %2 (%3)</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <source>Confirm send coins</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <source>Are you sure you want to send %1?</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <source> and </source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <source>The recepient address is not valid, please recheck.</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <source>The amount to pay must be larger than 0.</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <source>The amount exceeds your balance.</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <source>The total exceeds your balance when the %1 transaction fee is included.</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <source>Duplicate address found, can only send to each address once per send operation.</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <source>Error: Transaction creation failed.</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <source>Error: The transaction was rejected. This might happen if some of the coins in your wallet were already spent, such as if you used a copy of wallet.dat and coins were spent in the copy but not marked as spent here.</source>
        <translation type="unfinished"></translation>
    </message>
</context>
<context>
    <name>SendCoinsEntry</name>
    <message>
        <source>Form</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <source>A&amp;mount:</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <source>Pay &amp;To:</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <source>Enter a label for this address to add it to your address book</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <source>&amp;Label:</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <source>The address to send the payment to  (e.g. **********************************)</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <source>Choose address from address book</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <source>Alt+A</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <source>Paste address from clipboard</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <source>Alt+P</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <source>Remove this recipient</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <source>Enter a Bitcoin address (e.g. **********************************)</source>
        <translation type="unfinished"></translation>
    </message>
</context>
<context>
    <name>SignVerifyMessageDialog</name>
    <message>
        <source>Messaging - Sign / Verify a Message</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <source>&amp;Sign Message</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <source>You can sign messages with your addresses to prove you own them. Be careful not to sign anything vague, as phishing attacks may try to trick you into signing your identity over to them. Only sign fully-detailed statements you agree to.</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <source>The address to sign the message with (e.g. **********************************)</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <source>Choose an address from the address book</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <source>Alt+A</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <source>Paste address from clipboard</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <source>Alt+P</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <source>Enter the message you want to sign here</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <source>Copy the current signature to the system clipboard</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <source>Sign the message to prove you own this Bitcoin address</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <source>Reset all sign message fields</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <source>Clear &amp;All</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <source>&amp;Verify Message</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <source>Enter the signing address, message (ensure you copy line breaks, spaces, tabs, etc. exactly) and signature below to verify the message. Be careful not to read more into the signature than what is in the signed message itself, to avoid being tricked by a man-in-the-middle attack.</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <source>The address the message was signed with (e.g. **********************************)</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <source>Verify the message to ensure it was signed with the specified Bitcoin address</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <source>Reset all verify message fields</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <source>Enter a Bitcoin address (e.g. **********************************)</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <source>Click &quot;Sign Message&quot; to generate signature</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <source>Enter Bitcoin signature</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <source>The entered address is invalid.</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <source>Please check the address and try again.</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <source>The entered address does not refer to a key.</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <source>Wallet unlock was canceled.</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <source>Private key for the entered address is not available.</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <source>Message signing failed.</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <source>Message signed.</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <source>The signature could not be decoded.</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <source>Please check the signature and try again.</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <source>The signature did not match the message digest.</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <source>Message verification failed.</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <source>Message verified.</source>
        <translation type="unfinished"></translation>
    </message>
</context>
<context>
    <name>TransactionDesc</name>
    <message>
        <source>Open until %1</source>
        <translation type="unfinished"></translation>
    </message>
    <message numerus="yes">
        <source>Open for %n block(s)</source>
        <translation type="unfinished">
            <numerusform>Open for %n block</numerusform>
            <numerusform>Open for %n blocks</numerusform>
        </translation>
    </message>
    <message>
        <source>%1/offline</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <source>%1/unconfirmed</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <source>%1 confirmations</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <source>Status</source>
        <translation type="unfinished"></translation>
    </message>
    <message numerus="yes">
        <source>, broadcast through %n node(s)</source>
        <translation>
            <numerusform>, broadcast through %n node</numerusform>
            <numerusform>, broadcast through %n nodes</numerusform>
        </translation>
    </message>
    <message>
        <source>Date</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <source>Source</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <source>Generated</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <source>From</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <source>To</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <source>own address</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <source>label</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <source>Credit</source>
        <translation type="unfinished"></translation>
    </message>
    <message numerus="yes">
        <source>matures in %n more block(s)</source>
        <translation>
            <numerusform>matures in %n more block</numerusform>
            <numerusform>matures in %n more blocks</numerusform>
        </translation>
    </message>
    <message>
        <source>not accepted</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <source>Debit</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <source>Transaction fee</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <source>Net amount</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <source>Message</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <source>Comment</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <source>Transaction ID</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <source>Generated coins must mature 50 blocks before they can be spent. When you generated this block, it was broadcast to the network to be added to the block chain. If it fails to get into the chain, its state will change to &quot;not accepted&quot; and it won&apos;t be spendable. This may occasionally happen if another node generates a block within a few seconds of yours.</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <source>Debug information</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <source>Transaction</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <source>Inputs</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <source>Amount</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <source>true</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <source>false</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <source>, has not been successfully broadcast yet</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <source>unknown</source>
        <translation type="unfinished"></translation>
    </message>
</context>
<context>
    <name>TransactionDescDialog</name>
    <message>
        <source>Transaction details</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <source>This pane shows a detailed description of the transaction</source>
        <translation type="unfinished"></translation>
    </message>
</context>
<context>
    <name>TransactionTableModel</name>
    <message>
        <source>Date</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <source>Type</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <source>Address</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <source>Amount</source>
        <translation type="unfinished"></translation>
    </message>
    <message numerus="yes">
        <source>Open for %n block(s)</source>
        <translation>
            <numerusform>Open for %n block</numerusform>
            <numerusform>Open for %n blocks</numerusform>
        </translation>
    </message>
    <message>
        <source>Open until %1</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <source>Offline (%1 confirmations)</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <source>Unconfirmed (%1 of %2 confirmations)</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <source>Confirmed (%1 confirmations)</source>
        <translation type="unfinished"></translation>
    </message>
    <message numerus="yes">
        <source>Mined balance will be available when it matures in %n more block(s)</source>
        <translation>
            <numerusform>Mined balance will be available when it matures in %n more block</numerusform>
            <numerusform>Mined balance will be available when it matures in %n more blocks</numerusform>
        </translation>
    </message>
    <message>
        <source>This block was not received by any other nodes and will probably not be accepted!</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <source>Generated but not accepted</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <source>Received with</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <source>Received from</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <source>Sent to</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <source>Payment to yourself</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <source>Mined</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <source>(n/a)</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <source>Transaction status. Hover over this field to show number of confirmations.</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <source>Date and time that the transaction was received.</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <source>Type of transaction.</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <source>Destination address of transaction.</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <source>Amount removed from or added to balance.</source>
        <translation type="unfinished"></translation>
    </message>
</context>
<context>
    <name>TransactionView</name>
    <message>
        <source>All</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <source>Today</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <source>This week</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <source>This month</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <source>Last month</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <source>This year</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <source>Range...</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <source>Received with</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <source>Sent to</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <source>To yourself</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <source>Mined</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <source>Other</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <source>Enter address or label to search</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <source>Min amount</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <source>Copy address</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <source>Copy label</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <source>Copy amount</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <source>Edit label</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <source>Show transaction details</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <source>Export Transaction Data</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <source>Comma separated file (*.csv)</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <source>Confirmed</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <source>Date</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <source>Type</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <source>Label</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <source>Address</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <source>Amount</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <source>ID</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <source>Error exporting</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <source>Could not write to file %1.</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <source>Range:</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <source>to</source>
        <translation type="unfinished"></translation>
    </message>
</context>
<context>
    <name>WalletModel</name>
    <message>
        <source>Sending...</source>
        <translation type="unfinished"></translation>
    </message>
</context>
<context>
    <name>bitcoin-core</name>
    <message>
        <source>Bitcoin version</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <source>Usage:</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <source>Send command to -server or bitcoind</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <source>List commands</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <source>Get help for a command</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <source>Options:</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <source>Specify configuration file (default: bitcoin.conf)</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <source>Specify pid file (default: bitcoind.pid)</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <source>Generate coins</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <source>Don&apos;t generate coins</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <source>Specify data directory</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <source>Set database cache size in megabytes (default: 25)</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <source>Set database disk log size in megabytes (default: 100)</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <source>Specify connection timeout (in milliseconds)</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <source>Listen for connections on &lt;port&gt; (default: 8333 or testnet: 18333)</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <source>Maintain at most &lt;n&gt; connections to peers (default: 125)</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <source>Connect to a node to retrieve peer addresses, and disconnect</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <source>Specify your own public address</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <source>Bind to given address. Use [host]:port notation for IPv6</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <source>Threshold for disconnecting misbehaving peers (default: 100)</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <source>Number of seconds to keep misbehaving peers from reconnecting (default: 86400)</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <source>Detach block and address databases. Increases shutdown time (default: 0)</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <source>Accept command line and JSON-RPC commands</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <source>Run in the background as a daemon and accept commands</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <source>Use the test network</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <source>Accept connections from outside (default: 1 if no -proxy or -connect)</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <source>Connect only to the specified node(s)</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <source>Discover own IP address (default: 1 when listening and no -externalip)</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <source>Failed to listen on any port. Use -listen=0 if you want this.</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <source>Find peers using DNS lookup (default: 1 unless -connect)</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <source>Invalid -tor address: &apos;%s&apos;</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <source>Maximum per-connection receive buffer, &lt;n&gt;*1000 bytes (default: 5000)</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <source>Maximum per-connection send buffer, &lt;n&gt;*1000 bytes (default: 1000)</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <source>Only connect to nodes in network &lt;net&gt; (IPv4, IPv6 or Tor)</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <source>Output extra debugging information. Implies all other -debug* options</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <source>Output extra network debugging information</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <source>Prepend debug output with timestamp</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <source>Select the version of socks proxy to use (4-5, default: 5)</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <source>Send trace/debug info to console instead of debug.log file</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <source>Send trace/debug info to debugger</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <source>Use UPnP to map the listening port (default: 0)</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <source>Use UPnP to map the listening port (default: 1 when listening)</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <source>Use proxy to reach tor hidden services (default: same as -proxy)</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <source>Username for JSON-RPC connections</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <source>Warning: this version is obsolete, upgrade required</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <source>Password for JSON-RPC connections</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <source>Listen for JSON-RPC connections on &lt;port&gt; (default: 8332)</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <source>Allow JSON-RPC connections from specified IP address</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <source>Send commands to node running on &lt;ip&gt; (default: 127.0.0.1)</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <source>Execute command when the best block changes (%s in cmd is replaced by block hash)</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <source>Upgrade wallet to latest format</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <source>Set key pool size to &lt;n&gt; (default: 100)</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <source>Rescan the block chain for missing wallet transactions</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <source>How many blocks to check at startup (default: 2500, 0 = all)</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <source>How thorough the block verification is (0-6, default: 1)</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <source>Imports blocks from external blk000?.dat file</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <source>
SSL options: (see the Bitcoin Wiki for SSL setup instructions)</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <source>Use OpenSSL (https) for JSON-RPC connections</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <source>Server certificate file (default: server.cert)</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <source>Server private key (default: server.pem)</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <source>Acceptable ciphers (default: TLSv1+HIGH:!SSLv2:!aNULL:!eNULL:!AH:!3DES:@STRENGTH)</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <source>Warning: Disk space is low</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <source>This help message</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <source>Cannot obtain a lock on data directory %s.  Bitcoin is probably already running.</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <source>Bitcoin</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <source>Unable to bind to %s on this computer (bind returned error %d, %s)</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <source>Connect through socks proxy</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <source>Allow DNS lookups for -addnode, -seednode and -connect</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <source>Loading addresses...</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <source>Error loading blkindex.dat</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <source>Error loading wallet.dat: Wallet corrupted</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <source>Error loading wallet.dat: Wallet requires newer version of Bitcoin</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <source>Wallet needed to be rewritten: restart Bitcoin to complete</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <source>Error loading wallet.dat</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <source>Invalid -proxy address: &apos;%s&apos;</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <source>Unknown network specified in -onlynet: &apos;%s&apos;</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <source>Unknown -socks proxy version requested: %i</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <source>Cannot resolve -bind address: &apos;%s&apos;</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <source>Cannot resolve -externalip address: &apos;%s&apos;</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <source>Invalid amount for -paytxfee=&lt;amount&gt;: &apos;%s&apos;</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <source>Error: could not start node</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <source>Error: Wallet locked, unable to create transaction  </source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <source>Error: This transaction requires a transaction fee of at least %s because of its amount, complexity, or use of recently received funds  </source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <source>Error: Transaction creation failed  </source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <source>Sending...</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <source>Error: The transaction was rejected.  This might happen if some of the coins in your wallet were already spent, such as if you used a copy of wallet.dat and coins were spent in the copy but not marked as spent here.</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <source>Invalid amount</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <source>Insufficient funds</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <source>Loading block index...</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <source>Add a node to connect to and attempt to keep the connection open</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <source>Unable to bind to %s on this computer. Bitcoin is probably already running.</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <source>Find peers using internet relay chat (default: 0)</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <source>Fee per KB to add to transactions you send</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <source>Warning: -paytxfee is set very high. This is the transaction fee you will pay if you send a transaction.</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <source>Loading wallet...</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <source>Cannot downgrade wallet</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <source>Cannot initialize keypool</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <source>Cannot write default address</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <source>Rescanning...</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <source>Done loading</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <source>To use the %s option</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <source>%s, you must set a rpcpassword in the configuration file:
 %s
It is recommended you use the following random password:
rpcuser=bitcoinrpc
rpcpassword=%s
(you do not need to remember this password)
If the file does not exist, create it with owner-readable-only file permissions.
</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <source>Error</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <source>An error occured while setting up the RPC port %i for listening: %s</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <source>You must set rpcpassword=&lt;password&gt; in the configuration file:
%s
If the file does not exist, create it with owner-readable-only file permissions.</source>
        <translation type="unfinished"></translation>
    </message>
    <message>
        <source>Warning: Please check that your computer&apos;s date and time are correct.  If your clock is wrong Bitcoin will not work properly.</source>
        <translation type="unfinished"></translation>
    </message>
</context>
</TS>
