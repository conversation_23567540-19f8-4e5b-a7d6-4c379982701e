obj/noui.o: noui.cpp ui_interface.h util.h uint256.h netbase.h \
 serialize.h allocators.h version.h compat.h init.h wallet.h main.h \
 bignum.h net.h mruset.h protocol.h addrman.h sync.h key.h script.h \
 keystore.h crypter.h db.h scrypt.h bitcoinrpc.h \
 json/json_spirit_reader_template.h json/json_spirit_value.h \
 json/json_spirit_error_position.h json/json_spirit_writer_template.h \
 json/json_spirit_utils.h
noui.cpp ui_interface.h util.h uint256.h netbase.h :
 serialize.h allocators.h version.h compat.h init.h wallet.h main.h :
 bignum.h net.h mruset.h protocol.h addrman.h sync.h key.h script.h :
 keystore.h crypter.h db.h scrypt.h bitcoinrpc.h :
 json/json_spirit_reader_template.h json/json_spirit_value.h :
 json/json_spirit_error_position.h json/json_spirit_writer_template.h :
 json/json_spirit_utils.h :
