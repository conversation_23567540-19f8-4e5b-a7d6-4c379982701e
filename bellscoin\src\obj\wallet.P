obj/wallet.o: wallet.cpp wallet.h main.h bignum.h util.h uint256.h \
 netbase.h serialize.h allocators.h version.h compat.h net.h mruset.h \
 protocol.h addrman.h sync.h key.h script.h keystore.h crypter.h db.h \
 scrypt.h ui_interface.h walletdb.h base58.h
wallet.cpp wallet.h main.h bignum.h util.h uint256.h :
 netbase.h serialize.h allocators.h version.h compat.h net.h mruset.h :
 protocol.h addrman.h sync.h key.h script.h keystore.h crypter.h db.h :
 scrypt.h ui_interface.h walletdb.h base58.h :
