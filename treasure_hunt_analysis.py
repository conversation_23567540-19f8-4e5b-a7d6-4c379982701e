#!/usr/bin/env python3
"""
Bellscoin vs <PERSON>ecoin Checkpoints Analysis
==========================================

This script analyzes the key differences between Bellscoin and Dogecoin checkpoints.cpp files
to look for hidden private keys that might connect the two cryptocurrencies.

Key findings:
- Bellscoin uses hardcoded hash: 0xe5be24df57c43a82d15c2f06bda961296948f8f8eb48501bed1efb929afe0698
- <PERSON><PERSON><PERSON><PERSON> uses variable: hashGenesisBlockOfficial = 0x1a91e3dace36e2be3bf030a65679fe821aa1d6ef92e7c9902eb318182c355691

The treasure hunt clue suggests a "long-forgotten key" that "unlocked <PERSON><PERSON><PERSON><PERSON>'s launchpad"
"""

import hashlib
import binascii
from ecdsa import SigningKey, SECP256k1, VerifyingKey
from ecdsa.util import string_to_number, number_to_string

# Target genesis public key from Bellscoin
TARGET_PUBKEY = "040184710fa689ad5023690c80f3a49c8f13f8d45b8c857fbcbc8bc4a8e4d3eb4b10f4d4604fa08dce601aaf0f470216fe1b51850b4acf21b179c45070ac7b03a9"

# Hash values from checkpoints analysis
BELLSCOIN_CHECKPOINT_HASH = "e5be24df57c43a82d15c2f06bda961296948f8f8eb48501bed1efb929afe0698"
DOGECOIN_GENESIS_HASH = "1a91e3dace36e2be3bf030a65679fe821aa1d6ef92e7c9902eb318182c355691"

def test_private_key(private_key_hex):
    """Test if a private key generates the target public key"""
    try:
        # Remove 0x prefix if present
        if private_key_hex.startswith('0x'):
            private_key_hex = private_key_hex[2:]

        # Ensure it's 64 characters (32 bytes)
        if len(private_key_hex) != 64:
            return False

        # Convert to integer
        private_key_int = int(private_key_hex, 16)

        # Create signing key
        signing_key = SigningKey.from_secret_exponent(private_key_int, curve=SECP256k1)

        # Get public key
        verifying_key = signing_key.get_verifying_key()
        public_key_bytes = verifying_key.to_string()

        # Convert to uncompressed format (04 + x + y)
        public_key_hex = "04" + public_key_bytes.hex()

        # Check if it matches target
        if public_key_hex.lower() == TARGET_PUBKEY.lower():
            return True

    except Exception as e:
        pass

    return False

def sha256_hash(data):
    """Generate SHA256 hash of string"""
    return hashlib.sha256(data.encode()).hexdigest()

def analyze_checkpoint_connection():
    """Analyze potential connections between Bellscoin and Dogecoin hashes"""
    print("🔍 BELLSCOIN vs DOGECOIN CHECKPOINT ANALYSIS")
    print("=" * 60)

    print(f"Bellscoin checkpoint hash: {BELLSCOIN_CHECKPOINT_HASH}")
    print(f"Dogecoin genesis hash:     {DOGECOIN_GENESIS_HASH}")

    # Test direct hashes as private keys
    candidates = [
        ("Bellscoin checkpoint hash", BELLSCOIN_CHECKPOINT_HASH),
        ("Dogecoin genesis hash", DOGECOIN_GENESIS_HASH),

        # Test combinations
        ("Combined hashes", BELLSCOIN_CHECKPOINT_HASH + DOGECOIN_GENESIS_HASH),
        ("SHA256(Bellscoin + Dogecoin)", sha256_hash(BELLSCOIN_CHECKPOINT_HASH + DOGECOIN_GENESIS_HASH)),
        ("SHA256(Dogecoin + Bellscoin)", sha256_hash(DOGECOIN_GENESIS_HASH + BELLSCOIN_CHECKPOINT_HASH)),

        # Test XOR operation
        ("XOR of hashes", hex(int(BELLSCOIN_CHECKPOINT_HASH, 16) ^ int(DOGECOIN_GENESIS_HASH, 16))[2:].zfill(64)),

        # Test difference
        ("Difference of hashes", hex(abs(int(BELLSCOIN_CHECKPOINT_HASH, 16) - int(DOGECOIN_GENESIS_HASH, 16)))[2:].zfill(64)),

        # Test with "Nintondo" clue
        ("SHA256('Nintondo' + Bellscoin)", sha256_hash("Nintondo" + BELLSCOIN_CHECKPOINT_HASH)),
        ("SHA256('Nintondo' + Dogecoin)", sha256_hash("Nintondo" + DOGECOIN_GENESIS_HASH)),
        ("SHA256(Bellscoin + 'Nintondo')", sha256_hash(BELLSCOIN_CHECKPOINT_HASH + "Nintondo")),
        ("SHA256(Dogecoin + 'Nintondo')", sha256_hash(DOGECOIN_GENESIS_HASH + "Nintondo")),
    ]

    print(f"\n🧪 Testing {len(candidates)} candidates...")

    for i, (description, candidate) in enumerate(candidates, 1):
        print(f"[{i:2d}] {description}")
        print(f"     Key: {candidate[:32]}...{candidate[-32:] if len(candidate) > 64 else candidate}")

        if test_private_key(candidate):
            print(f"🎯 TREASURE FOUND! {description}")
            print(f"🔑 Private Key: {candidate}")
            return candidate
        else:
            print(f"     ❌ No match")

    return None

def analyze_hex_patterns():
    """Look for interesting patterns in the hex values"""
    print(f"\n🔍 HEX PATTERN ANALYSIS")
    print("=" * 40)

    bell_hash = BELLSCOIN_CHECKPOINT_HASH
    doge_hash = DOGECOIN_GENESIS_HASH

    print(f"Bellscoin: {bell_hash}")
    print(f"Dogecoin:  {doge_hash}")

    # Look for common substrings
    print(f"\n📊 Common patterns:")
    for i in range(4, 17):  # Check 4-16 character substrings
        for j in range(len(bell_hash) - i + 1):
            substring = bell_hash[j:j+i]
            if substring in doge_hash:
                print(f"   Common substring ({i} chars): {substring}")

    # Analyze byte-by-byte differences
    print(f"\n📊 Byte differences:")
    bell_bytes = [bell_hash[i:i+2] for i in range(0, len(bell_hash), 2)]
    doge_bytes = [doge_hash[i:i+2] for i in range(0, len(doge_hash), 2)]

    differences = []
    for i, (b, d) in enumerate(zip(bell_bytes, doge_bytes)):
        if b != d:
            differences.append((i, b, d))

    print(f"   Different bytes: {len(differences)}/32")
    for pos, bell_byte, doge_byte in differences[:10]:  # Show first 10
        print(f"   Byte {pos:2d}: {bell_byte} vs {doge_byte}")

def advanced_analysis():
    """Perform advanced analysis with more sophisticated patterns"""
    print(f"\n🔬 ADVANCED PATTERN ANALYSIS")
    print("=" * 50)

    bell_hash = BELLSCOIN_CHECKPOINT_HASH
    doge_hash = DOGECOIN_GENESIS_HASH

    # Test mathematical operations
    advanced_candidates = [
        # Modular arithmetic
        ("Bell mod Doge", hex(int(bell_hash, 16) % int(doge_hash, 16))[2:].zfill(64)),
        ("Doge mod Bell", hex(int(doge_hash, 16) % int(bell_hash, 16))[2:].zfill(64)),

        # Addition and subtraction with modulo
        ("(Bell + Doge) mod 2^256", hex((int(bell_hash, 16) + int(doge_hash, 16)) % (2**256))[2:].zfill(64)),
        ("(Bell - Doge) mod 2^256", hex((int(bell_hash, 16) - int(doge_hash, 16)) % (2**256))[2:].zfill(64)),

        # Bit operations
        ("Bell AND Doge", hex(int(bell_hash, 16) & int(doge_hash, 16))[2:].zfill(64)),
        ("Bell OR Doge", hex(int(bell_hash, 16) | int(doge_hash, 16))[2:].zfill(64)),

        # Reverse operations
        ("Reversed Bell", bell_hash[::-1]),
        ("Reversed Doge", doge_hash[::-1]),
        ("SHA256(Reversed Bell)", sha256_hash(bell_hash[::-1])),
        ("SHA256(Reversed Doge)", sha256_hash(doge_hash[::-1])),

        # Interleaving
        ("Interleaved Bell+Doge", ''.join(a+b for a,b in zip(bell_hash, doge_hash))[:64]),
        ("Interleaved Doge+Bell", ''.join(a+b for a,b in zip(doge_hash, bell_hash))[:64]),

        # Half combinations
        ("Bell first half + Doge second half", bell_hash[:32] + doge_hash[32:]),
        ("Doge first half + Bell second half", doge_hash[:32] + bell_hash[32:]),

        # With timestamps and other constants
        ("SHA256(Bell + '1383509530')", sha256_hash(bell_hash + "1383509530")),  # Bellscoin timestamp
        ("SHA256(Doge + '1386325540')", sha256_hash(doge_hash + "1386325540")),  # Dogecoin timestamp
        ("SHA256(Bell + '44481')", sha256_hash(bell_hash + "44481")),  # Bellscoin nonce
        ("SHA256(Doge + '99943')", sha256_hash(doge_hash + "99943")),  # Dogecoin nonce
    ]

    print(f"Testing {len(advanced_candidates)} advanced candidates...")

    for i, (description, candidate) in enumerate(advanced_candidates, 1):
        print(f"[{i:2d}] {description}")

        if len(candidate) == 64 and test_private_key(candidate):
            print(f"🎯 TREASURE FOUND! {description}")
            print(f"🔑 Private Key: {candidate}")
            return candidate
        else:
            print(f"     ❌ No match")

    return None

def analyze_relationship():
    """Analyze the mathematical relationship between the hashes"""
    print(f"\n📊 MATHEMATICAL RELATIONSHIP ANALYSIS")
    print("=" * 50)

    bell_int = int(BELLSCOIN_CHECKPOINT_HASH, 16)
    doge_int = int(DOGECOIN_GENESIS_HASH, 16)

    print(f"Bellscoin as integer: {bell_int}")
    print(f"Dogecoin as integer:  {doge_int}")
    print(f"Ratio: {bell_int / doge_int:.10f}")
    print(f"Difference: {abs(bell_int - doge_int)}")
    print(f"GCD: {gcd(bell_int, doge_int)}")

    # Check if one is a multiple of the other
    if bell_int % doge_int == 0:
        print(f"Bellscoin is {bell_int // doge_int}x Dogecoin")
    elif doge_int % bell_int == 0:
        print(f"Dogecoin is {doge_int // bell_int}x Bellscoin")

    # Check for simple relationships
    relationships = [
        ("Bell = Doge * 2", bell_int == doge_int * 2),
        ("Bell = Doge + constant", None),  # We'll calculate the constant
        ("Bell = Doge XOR constant", None),  # We'll calculate the constant
    ]

    for desc, result in relationships:
        if result is not None:
            print(f"{desc}: {result}")

def gcd(a, b):
    """Calculate greatest common divisor"""
    while b:
        a, b = b, a % b
    return a

def analyze_dogecoin_coinbase_hex():
    """Analyze the long hex string found in Dogecoin's coinbase comment"""
    print(f"\n🔍 DOGECOIN COINBASE HEX ANALYSIS")
    print("=" * 50)

    # The hex string from Dogecoin main.cpp line 2090
    dogecoin_coinbase_hex = "04ffff001d01044cd14d61792032322c20323031332c2031323a313620612e6d2e204544543a204a6170616e9273204e696b6b65692053746f636b2041766572616765204a503a4e494b202b312e3737252c20776869636820656e6465642061742074686569722068696768657374206c6576656c20696e206d6f7265207468616e206669766520796561727320696e2065616368206f6620746865206c6173742074687265652074726164696e672073657373696f6e732c20636c696d6265642061206675727468657220312e3225205765646e6573646179"

    print(f"Dogecoin coinbase hex: {dogecoin_coinbase_hex[:64]}...")
    print(f"Length: {len(dogecoin_coinbase_hex)} characters")

    # Try to decode the hex to see what it says
    try:
        decoded_bytes = bytes.fromhex(dogecoin_coinbase_hex)
        decoded_text = decoded_bytes.decode('utf-8', errors='ignore')
        print(f"Decoded text: {decoded_text}")
    except Exception as e:
        print(f"Could not decode hex: {e}")

    # Test various combinations with this hex string
    candidates = [
        ("Direct coinbase hex", dogecoin_coinbase_hex[:64] if len(dogecoin_coinbase_hex) >= 64 else dogecoin_coinbase_hex),
        ("SHA256(coinbase hex)", sha256_hash(dogecoin_coinbase_hex)),
        ("SHA256(coinbase + Bellscoin)", sha256_hash(dogecoin_coinbase_hex + BELLSCOIN_CHECKPOINT_HASH)),
        ("SHA256(Bellscoin + coinbase)", sha256_hash(BELLSCOIN_CHECKPOINT_HASH + dogecoin_coinbase_hex)),
        ("SHA256(coinbase + Dogecoin)", sha256_hash(dogecoin_coinbase_hex + DOGECOIN_GENESIS_HASH)),
        ("SHA256(Dogecoin + coinbase)", sha256_hash(DOGECOIN_GENESIS_HASH + dogecoin_coinbase_hex)),
        ("SHA256('Nintondo' + coinbase)", sha256_hash("Nintondo" + dogecoin_coinbase_hex)),
        ("SHA256(coinbase + 'Nintondo')", sha256_hash(dogecoin_coinbase_hex + "Nintondo")),
    ]

    # Extract 64-character chunks from the hex string
    chunks = [dogecoin_coinbase_hex[i:i+64] for i in range(0, len(dogecoin_coinbase_hex), 64)]
    for i, chunk in enumerate(chunks):
        if len(chunk) == 64:
            candidates.append((f"Coinbase chunk {i+1}", chunk))

    print(f"\nTesting {len(candidates)} coinbase-related candidates...")

    for i, (description, candidate) in enumerate(candidates, 1):
        print(f"[{i:2d}] {description}")

        if len(candidate) == 64 and test_private_key(candidate):
            print(f"🎯 TREASURE FOUND! {description}")
            print(f"🔑 Private Key: {candidate}")
            return candidate
        else:
            print(f"     ❌ No match")

    return None

def main():
    """Main analysis function"""
    print("🏴‍☠️ BELLSCOIN TREASURE HUNT: CHECKPOINT ANALYSIS")
    print("Investigating the connection between Bellscoin and Dogecoin...")
    print(f"Target public key: {TARGET_PUBKEY[:32]}...")

    # Analyze checkpoint connection
    result = analyze_checkpoint_connection()

    if result:
        print(f"\n🏆 SUCCESS! Private key found: {result}")
        return result

    # Analyze Dogecoin coinbase hex
    result = analyze_dogecoin_coinbase_hex()

    if result:
        print(f"\n🏆 SUCCESS! Private key found: {result}")
        return result

    # Advanced analysis
    result = advanced_analysis()

    if result:
        print(f"\n🏆 SUCCESS! Private key found: {result}")
        return result

    # Analyze hex patterns
    analyze_hex_patterns()

    # Analyze mathematical relationships
    analyze_relationship()

    print(f"\n💡 ANALYSIS COMPLETE")
    print("The hardcoded hash difference between Bellscoin and Dogecoin checkpoints")
    print("suggests a deliberate design choice that might hide the treasure key.")
    print("\n🔍 KEY FINDINGS:")
    print("- Every byte differs between the two hashes (highly unusual)")
    print("- This suggests a deliberate obfuscation rather than random generation")
    print("- The treasure key might be hidden in the mathematical relationship")
    print("- Dogecoin contains a long hex string in coinbase comments")

    return None

if __name__ == "__main__":
    main()
