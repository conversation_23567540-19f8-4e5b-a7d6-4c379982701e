<RCC>
    <qresource prefix="/icons">
        <file alias="bitcoin">res/icons/bitcoin.png</file>
        <file alias="address-book">res/icons/address-book.png</file>
        <file alias="quit">res/icons/quit.png</file>
        <file alias="send">res/icons/send.png</file>
        <file alias="toolbar">res/icons/toolbar.png</file>
        <file alias="connect_0">res/icons/connect0_16.png</file>
        <file alias="connect_1">res/icons/connect1_16.png</file>
        <file alias="connect_2">res/icons/connect2_16.png</file>
        <file alias="connect_3">res/icons/connect3_16.png</file>
        <file alias="connect_4">res/icons/connect4_16.png</file>
        <file alias="mining_active">res/icons/mining_active.png</file>
        <file alias="mining_inactive">res/icons/mining_inactive.png</file>
        <file alias="transaction_0">res/icons/transaction0.png</file>
        <file alias="transaction_confirmed">res/icons/transaction2.png</file>
        <file alias="transaction_1">res/icons/clock1.png</file>
        <file alias="transaction_2">res/icons/clock2.png</file>
        <file alias="transaction_3">res/icons/clock3.png</file>
        <file alias="transaction_4">res/icons/clock4.png</file>
        <file alias="transaction_5">res/icons/clock5.png</file>
        <file alias="options">res/icons/configure.png</file>
        <file alias="receiving_addresses">res/icons/receive.png</file>
        <file alias="editpaste">res/icons/editpaste.png</file>
        <file alias="editcopy">res/icons/editcopy.png</file>
        <file alias="add">res/icons/add.png</file>
        <file alias="bitcoin_testnet">res/icons/bitcoin_testnet.png</file>
        <file alias="toolbar_testnet">res/icons/toolbar_testnet.png</file>
        <file alias="edit">res/icons/edit.png</file>
        <file alias="history">res/icons/history.png</file>
        <file alias="overview">res/icons/overview.png</file>
        <file alias="mining">res/icons/mining.png</file>
        <file alias="export">res/icons/export.png</file>
        <file alias="synced">res/icons/synced.png</file>
        <file alias="remove">res/icons/remove.png</file>
        <file alias="tx_mined">res/icons/tx_mined.png</file>
        <file alias="tx_input">res/icons/tx_input.png</file>
        <file alias="tx_output">res/icons/tx_output.png</file>
        <file alias="tx_inout">res/icons/tx_inout.png</file>
        <file alias="lock_closed">res/icons/lock_closed.png</file>
        <file alias="lock_open">res/icons/lock_open.png</file>
        <file alias="key">res/icons/key.png</file>
        <file alias="filesave">res/icons/filesave.png</file>
        <file alias="qrcode">res/icons/qrcode.png</file>
        <file alias="debugwindow">res/icons/debugwindow.png</file>
    </qresource>
    <qresource prefix="/images">
        <file alias="about">res/images/about.png</file>
        <file alias="splash">res/images/splash2.jpg</file>
    </qresource>
    <qresource prefix="/movies">
        <file alias="update_spinner">res/movies/update_spinner.mng</file>
    </qresource>
    <qresource prefix="/translations">
        <file alias="bg">locale/bitcoin_bg.qm</file>
        <file alias="ca_ES">locale/bitcoin_ca_ES.qm</file>
        <file alias="cs">locale/bitcoin_cs.qm</file>
        <file alias="da">locale/bitcoin_da.qm</file>
        <file alias="de">locale/bitcoin_de.qm</file>
        <file alias="el_GR">locale/bitcoin_el_GR.qm</file>
        <file alias="en">locale/bitcoin_en.qm</file>
        <file alias="es">locale/bitcoin_es.qm</file>
        <file alias="es_CL">locale/bitcoin_es_CL.qm</file>
        <file alias="et">locale/bitcoin_et.qm</file>
        <file alias="eu_ES">locale/bitcoin_eu_ES.qm</file>
        <file alias="fa">locale/bitcoin_fa.qm</file>
        <file alias="fa_IR">locale/bitcoin_fa_IR.qm</file>
        <file alias="fi">locale/bitcoin_fi.qm</file>
        <file alias="fr">locale/bitcoin_fr.qm</file>
        <file alias="fr_CA">locale/bitcoin_fr_CA.qm</file>
        <file alias="he">locale/bitcoin_he.qm</file>
        <file alias="hr">locale/bitcoin_hr.qm</file>
        <file alias="hu">locale/bitcoin_hu.qm</file>
        <file alias="it">locale/bitcoin_it.qm</file>
        <file alias="lt">locale/bitcoin_lt.qm</file>
        <file alias="nb">locale/bitcoin_nb.qm</file>
        <file alias="nl">locale/bitcoin_nl.qm</file>
        <file alias="pl">locale/bitcoin_pl.qm</file>
        <file alias="pt_BR">locale/bitcoin_pt_BR.qm</file>
        <file alias="pt_PT">locale/bitcoin_pt_PT.qm</file>
        <file alias="ro_RO">locale/bitcoin_ro_RO.qm</file>
        <file alias="ru">locale/bitcoin_ru.qm</file>
        <file alias="sk">locale/bitcoin_sk.qm</file>
        <file alias="sr">locale/bitcoin_sr.qm</file>
        <file alias="sv">locale/bitcoin_sv.qm</file>
        <file alias="tr">locale/bitcoin_tr.qm</file>
        <file alias="uk">locale/bitcoin_uk.qm</file>
        <file alias="zh_CN">locale/bitcoin_zh_CN.qm</file>
        <file alias="zh_TW">locale/bitcoin_zh_TW.qm</file>
    </qresource>
</RCC>
