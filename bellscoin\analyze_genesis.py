# Genesis block parameters
version = 1
prev_block = "0000000000000000000000000000000000000000000000000000000000000000"
merkle_root = "5b2a3f53f605d62c53e62932dac6925e3d74afa5a4b459745c36d42d0ed26a69"
timestamp = 1383509530
bits = 0x1e0ffff0
nonce = 44481

# Combine these in different ways
import hashlib

def try_combinations():
    combinations = []
    
    # Combine timestamp and nonce
    combinations.append(f"{timestamp}{nonce}")
    
    # Combine bits and nonce
    combinations.append(f"{bits:x}{nonce}")
    
    # Hash of all parameters
    all_params = f"{version}{prev_block}{merkle_root}{timestamp}{bits:x}{nonce}"
    combinations.append(hashlib.sha256(all_params.encode()).hexdigest())
    
    # Try with the message start bytes
    message_start = "c0c0c0c0"
    combinations.append(f"{message_start}{nonce}")
    
    return combinations

potential_seeds = try_combinations()
for i, seed in enumerate(potential_seeds):
    print(f"Potential seed {i+1}: {seed}")