<?xml version="1.0" ?><!DOCTYPE TS><TS language="pt_PT" version="2.0">
<defaultcodec>UTF-8</defaultcodec>
<context>
    <name>AboutDialog</name>
    <message>
        <location filename="../forms/aboutdialog.ui" line="14"/>
        <source>About Bitcoin</source>
        <translation>Sobre o Bitcoin</translation>
    </message>
    <message>
        <location filename="../forms/aboutdialog.ui" line="53"/>
        <source>&lt;b&gt;Bitcoin&lt;/b&gt; version</source>
        <translation>&lt;b&gt;Bitcoin&lt;/b&gt; versão</translation>
    </message>
    <message>
        <location filename="../forms/aboutdialog.ui" line="97"/>
        <source>Copyright © 2009-2012 Bitcoin Developers

This is experimental software.

Distributed under the MIT/X11 software license, see the accompanying file license.txt or http://www.opensource.org/licenses/mit-license.php.

This product includes software developed by the OpenSSL Project for use in the OpenSSL Toolkit (http://www.openssl.org/) and cryptographic software written by <PERSON> (<EMAIL>) and UPnP software written by <PERSON>.</source>
        <translation>Todos os direitos reservados © 2009-2012 Programadores Bitcoin

Este é um programa experimental.

Distribuído sobre uma licença de software MIT/X11, por favor verifique o ficheiro anexo license.txt ou http://www.opensource.org/licenses/mit-license.php.

Este produto inclui software desenvolvido pelo Projecto OpenSSL para uso no OpenSSL Toolkit (http://www.openssl.org/), software criptográfico escrito por Eric Young (<EMAIL>) e software UPnP escrito por Thomas Bernard.</translation>
    </message>
</context>
<context>
    <name>AddressBookPage</name>
    <message>
        <location filename="../forms/addressbookpage.ui" line="14"/>
        <source>Address Book</source>
        <translation>Livro de endereços</translation>
    </message>
    <message>
        <location filename="../forms/addressbookpage.ui" line="20"/>
        <source>These are your Bitcoin addresses for receiving payments.  You may want to give a different one to each sender so you can keep track of who is paying you.</source>
        <translation>Estes são os seus endereços Bitcoin para receber pagamentos. Poderá enviar um endereço diferente para cada remetente para poder identificar os pagamentos.</translation>
    </message>
    <message>
        <location filename="../forms/addressbookpage.ui" line="36"/>
        <source>Double-click to edit address or label</source>
        <translation>Clique duas vezes para editar o endereço ou o rótulo</translation>
    </message>
    <message>
        <location filename="../forms/addressbookpage.ui" line="63"/>
        <source>Create a new address</source>
        <translation>Criar um novo endereço</translation>
    </message>
    <message>
        <location filename="../forms/addressbookpage.ui" line="77"/>
        <source>Copy the currently selected address to the system clipboard</source>
        <translation>Copie o endereço selecionado para a área de transferência</translation>
    </message>
    <message>
        <location filename="../forms/addressbookpage.ui" line="66"/>
        <source>&amp;New Address</source>
        <translation type="unfinished"/>
    </message>
    <message>
        <location filename="../forms/addressbookpage.ui" line="80"/>
        <source>&amp;Copy Address</source>
        <translation type="unfinished"/>
    </message>
    <message>
        <location filename="../forms/addressbookpage.ui" line="91"/>
        <source>Show &amp;QR Code</source>
        <translation>Mostrar &amp;Código QR</translation>
    </message>
    <message>
        <location filename="../forms/addressbookpage.ui" line="102"/>
        <source>Sign a message to prove you own this address</source>
        <translation>Assine uma mensagem para provar que é dono deste endereço</translation>
    </message>
    <message>
        <location filename="../forms/addressbookpage.ui" line="105"/>
        <source>&amp;Sign Message</source>
        <translation>&amp;Assinar Mensagem</translation>
    </message>
    <message>
        <location filename="../forms/addressbookpage.ui" line="116"/>
        <source>Delete the currently selected address from the list. Only sending addresses can be deleted.</source>
        <translation>Eliminar o endereço selecionado da lista. Apenas endereços de envio podem ser eliminados.</translation>
    </message>
    <message>
        <location filename="../forms/addressbookpage.ui" line="119"/>
        <source>&amp;Delete</source>
        <translation>&amp;Eliminar</translation>
    </message>
    <message>
        <location filename="../addressbookpage.cpp" line="63"/>
        <source>Copy &amp;Label</source>
        <translation type="unfinished"/>
    </message>
    <message>
        <location filename="../addressbookpage.cpp" line="65"/>
        <source>&amp;Edit</source>
        <translation type="unfinished"/>
    </message>
    <message>
        <location filename="../addressbookpage.cpp" line="292"/>
        <source>Export Address Book Data</source>
        <translation>Exportar de dados do Livro de Endereços</translation>
    </message>
    <message>
        <location filename="../addressbookpage.cpp" line="293"/>
        <source>Comma separated file (*.csv)</source>
        <translation>Ficheiro separado por vírgulas (*.csv)</translation>
    </message>
    <message>
        <location filename="../addressbookpage.cpp" line="306"/>
        <source>Error exporting</source>
        <translation>Erro ao exportar</translation>
    </message>
    <message>
        <location filename="../addressbookpage.cpp" line="306"/>
        <source>Could not write to file %1.</source>
        <translation>Could not write to file %1.</translation>
    </message>
</context>
<context>
    <name>AddressTableModel</name>
    <message>
        <location filename="../addresstablemodel.cpp" line="142"/>
        <source>Label</source>
        <translation>Rótulo</translation>
    </message>
    <message>
        <location filename="../addresstablemodel.cpp" line="142"/>
        <source>Address</source>
        <translation>Endereço</translation>
    </message>
    <message>
        <location filename="../addresstablemodel.cpp" line="178"/>
        <source>(no label)</source>
        <translation>(Sem rótulo)</translation>
    </message>
</context>
<context>
    <name>AskPassphraseDialog</name>
    <message>
        <location filename="../forms/askpassphrasedialog.ui" line="26"/>
        <source>Passphrase Dialog</source>
        <translation type="unfinished"/>
    </message>
    <message>
        <location filename="../forms/askpassphrasedialog.ui" line="47"/>
        <source>Enter passphrase</source>
        <translation>Escreva a frase de segurança</translation>
    </message>
    <message>
        <location filename="../forms/askpassphrasedialog.ui" line="61"/>
        <source>New passphrase</source>
        <translation>Nova frase de segurança</translation>
    </message>
    <message>
        <location filename="../forms/askpassphrasedialog.ui" line="75"/>
        <source>Repeat new passphrase</source>
        <translation>Repita a nova frase de segurança</translation>
    </message>
    <message>
        <location filename="../askpassphrasedialog.cpp" line="33"/>
        <source>Enter the new passphrase to the wallet.&lt;br/&gt;Please use a passphrase of &lt;b&gt;10 or more random characters&lt;/b&gt;, or &lt;b&gt;eight or more words&lt;/b&gt;.</source>
        <translation>Escreva a nova frase de seguraça da sua carteira. &lt;br/&gt; Por favor, use uma frase de &lt;b&gt;10 ou mais caracteres aleatórios,&lt;/b&gt; ou &lt;b&gt;oito ou mais palavras.&lt;/b&gt;</translation>
    </message>
    <message>
        <location filename="../askpassphrasedialog.cpp" line="34"/>
        <source>Encrypt wallet</source>
        <translation>Encriptar carteira</translation>
    </message>
    <message>
        <location filename="../askpassphrasedialog.cpp" line="37"/>
        <source>This operation needs your wallet passphrase to unlock the wallet.</source>
        <translation>A sua frase de segurança é necessária para desbloquear a carteira.</translation>
    </message>
    <message>
        <location filename="../askpassphrasedialog.cpp" line="42"/>
        <source>Unlock wallet</source>
        <translation>Desbloquear carteira</translation>
    </message>
    <message>
        <location filename="../askpassphrasedialog.cpp" line="45"/>
        <source>This operation needs your wallet passphrase to decrypt the wallet.</source>
        <translation>A sua frase de segurança é necessária para desencriptar a carteira.</translation>
    </message>
    <message>
        <location filename="../askpassphrasedialog.cpp" line="50"/>
        <source>Decrypt wallet</source>
        <translation>Desencriptar carteira</translation>
    </message>
    <message>
        <location filename="../askpassphrasedialog.cpp" line="53"/>
        <source>Change passphrase</source>
        <translation>Alterar frase de segurança</translation>
    </message>
    <message>
        <location filename="../askpassphrasedialog.cpp" line="54"/>
        <source>Enter the old and new passphrase to the wallet.</source>
        <translation>Escreva a frase de segurança antiga seguida da nova para a carteira.</translation>
    </message>
    <message>
        <location filename="../askpassphrasedialog.cpp" line="100"/>
        <source>Confirm wallet encryption</source>
        <translation>Confirmar encriptação da carteira</translation>
    </message>
    <message>
        <location filename="../askpassphrasedialog.cpp" line="101"/>
        <source>WARNING: If you encrypt your wallet and lose your passphrase, you will &lt;b&gt;LOSE ALL OF YOUR BITCOINS&lt;/b&gt;!
Are you sure you wish to encrypt your wallet?</source>
        <translation>AVISO: Se encriptar a carteira e perder a sua senha irá &lt;b&gt;perder todos os seus BITCOINS!&lt;/b&gt; Tem a certeza de que deseja encriptar a carteira?</translation>
    </message>
    <message>
        <location filename="../askpassphrasedialog.cpp" line="110"/>
        <location filename="../askpassphrasedialog.cpp" line="159"/>
        <source>Wallet encrypted</source>
        <translation>Carteira encriptada</translation>
    </message>
    <message>
        <location filename="../askpassphrasedialog.cpp" line="111"/>
        <source>Bitcoin will close now to finish the encryption process. Remember that encrypting your wallet cannot fully protect your bitcoins from being stolen by malware infecting your computer.</source>
        <translation>O cliente Bitcoin irá agora ser fechado para terminar o processo de encriptação. Recorde que a encriptação da sua carteira não protegerá totalmente os seus bitcoins de serem roubados por programas maliciosos que infectem o seu computador.</translation>
    </message>
    <message>
        <location filename="../askpassphrasedialog.cpp" line="207"/>
        <location filename="../askpassphrasedialog.cpp" line="231"/>
        <source>Warning: The Caps Lock key is on.</source>
        <translation>Atenção: A tecla Caps Lock está activa.</translation>
    </message>
    <message>
        <location filename="../askpassphrasedialog.cpp" line="116"/>
        <location filename="../askpassphrasedialog.cpp" line="123"/>
        <location filename="../askpassphrasedialog.cpp" line="165"/>
        <location filename="../askpassphrasedialog.cpp" line="171"/>
        <source>Wallet encryption failed</source>
        <translation>A encriptação da carteira falhou</translation>
    </message>
    <message>
        <location filename="../askpassphrasedialog.cpp" line="117"/>
        <source>Wallet encryption failed due to an internal error. Your wallet was not encrypted.</source>
        <translation>A encriptação da carteira falhou devido a um erro interno. A carteira não foi encriptada.</translation>
    </message>
    <message>
        <location filename="../askpassphrasedialog.cpp" line="124"/>
        <location filename="../askpassphrasedialog.cpp" line="172"/>
        <source>The supplied passphrases do not match.</source>
        <translation>As frases de segurança fornecidas não coincidem.</translation>
    </message>
    <message>
        <location filename="../askpassphrasedialog.cpp" line="135"/>
        <source>Wallet unlock failed</source>
        <translation>O desbloqueio da carteira falhou</translation>
    </message>
    <message>
        <location filename="../askpassphrasedialog.cpp" line="136"/>
        <location filename="../askpassphrasedialog.cpp" line="147"/>
        <location filename="../askpassphrasedialog.cpp" line="166"/>
        <source>The passphrase entered for the wallet decryption was incorrect.</source>
        <translation>A frase de segurança introduzida para a desencriptação da carteira estava incorreta.</translation>
    </message>
    <message>
        <location filename="../askpassphrasedialog.cpp" line="146"/>
        <source>Wallet decryption failed</source>
        <translation>A desencriptação da carteira falhou</translation>
    </message>
    <message>
        <location filename="../askpassphrasedialog.cpp" line="160"/>
        <source>Wallet passphrase was succesfully changed.</source>
        <translation>A frase de segurança da carteira foi alterada com êxito.</translation>
    </message>
</context>
<context>
    <name>BitcoinGUI</name>
    <message>
        <location filename="../bitcoingui.cpp" line="73"/>
        <source>Bitcoin Wallet</source>
        <translation>Carteira Bitcoin</translation>
    </message>
    <message>
        <location filename="../bitcoingui.cpp" line="215"/>
        <source>Sign &amp;message...</source>
        <translation type="unfinished"/>
    </message>
    <message>
        <location filename="../bitcoingui.cpp" line="248"/>
        <source>Show/Hide &amp;Bitcoin</source>
        <translation>Mostrar/Ocultar &amp;Bitcoin</translation>
    </message>
    <message>
        <location filename="../bitcoingui.cpp" line="515"/>
        <source>Synchronizing with network...</source>
        <translation>Sincronizando com a rede...</translation>
    </message>
    <message>
        <location filename="../bitcoingui.cpp" line="185"/>
        <source>&amp;Overview</source>
        <translation>&amp;Visão geral</translation>
    </message>
    <message>
        <location filename="../bitcoingui.cpp" line="186"/>
        <source>Show general overview of wallet</source>
        <translation>Mostrar visão geral da carteira</translation>
    </message>
    <message>
        <location filename="../bitcoingui.cpp" line="191"/>
        <source>&amp;Transactions</source>
        <translation>&amp;Transações</translation>
    </message>
    <message>
        <location filename="../bitcoingui.cpp" line="192"/>
        <source>Browse transaction history</source>
        <translation>Navegar pelo histórico de transações</translation>
    </message>
    <message>
        <location filename="../bitcoingui.cpp" line="197"/>
        <source>&amp;Address Book</source>
        <translation>&amp;Livro de endereços</translation>
    </message>
    <message>
        <location filename="../bitcoingui.cpp" line="198"/>
        <source>Edit the list of stored addresses and labels</source>
        <translation>Editar a lista de endereços e rótulos</translation>
    </message>
    <message>
        <location filename="../bitcoingui.cpp" line="203"/>
        <source>&amp;Much receive</source>
        <translation>&amp;Receber moedas</translation>
    </message>
    <message>
        <location filename="../bitcoingui.cpp" line="204"/>
        <source>Show the list of addresses for receiving payments</source>
        <translation>Mostrar a lista de endereços para receber pagamentos</translation>
    </message>
    <message>
        <location filename="../bitcoingui.cpp" line="209"/>
        <source>&amp;Send coins</source>
        <translation>&amp;Enviar moedas</translation>
    </message>
    <message>
        <location filename="../bitcoingui.cpp" line="216"/>
        <source>Prove you control an address</source>
        <translation>Prove que controla um endereço</translation>
    </message>
    <message>
        <location filename="../bitcoingui.cpp" line="235"/>
        <source>E&amp;xit</source>
        <translation>S&amp;air</translation>
    </message>
    <message>
        <location filename="../bitcoingui.cpp" line="236"/>
        <source>Quit application</source>
        <translation>Sair da aplicação</translation>
    </message>
    <message>
        <location filename="../bitcoingui.cpp" line="239"/>
        <source>&amp;About %1</source>
        <translation>&amp;Acerca de %1</translation>
    </message>
    <message>
        <location filename="../bitcoingui.cpp" line="240"/>
        <source>Show information about Bitcoin</source>
        <translation>Mostrar informação sobre Bitcoin</translation>
    </message>
    <message>
        <location filename="../bitcoingui.cpp" line="242"/>
        <source>About &amp;Qt</source>
        <translation>Sobre &amp;Qt</translation>
    </message>
    <message>
        <location filename="../bitcoingui.cpp" line="243"/>
        <source>Show information about Qt</source>
        <translation>Mostrar informação sobre Qt</translation>
    </message>
    <message>
        <location filename="../bitcoingui.cpp" line="245"/>
        <source>&amp;Options...</source>
        <translation>&amp;Opções...</translation>
    </message>
    <message>
        <location filename="../bitcoingui.cpp" line="252"/>
        <source>&amp;Encrypt Wallet...</source>
        <translation type="unfinished"/>
    </message>
    <message>
        <location filename="../bitcoingui.cpp" line="255"/>
        <source>&amp;Backup Wallet...</source>
        <translation type="unfinished"/>
    </message>
    <message>
        <location filename="../bitcoingui.cpp" line="257"/>
        <source>&amp;Change Passphrase...</source>
        <translation type="unfinished"/>
    </message>
    <message numerus="yes">
        <location filename="../bitcoingui.cpp" line="517"/>
        <source>~%n block(s) remaining</source>
        <translation><numerusform>~%n bloco restante</numerusform><numerusform>~%n blocos restantes</numerusform></translation>
    </message>
    <message>
        <location filename="../bitcoingui.cpp" line="528"/>
        <source>Downloaded %1 of %2 blocks of transaction history (%3% done).</source>
        <translation>Recuperados %1 de %2 blocos do histórico de transações (%3% completo)</translation>
    </message>
    <message>
        <location filename="../bitcoingui.cpp" line="250"/>
        <source>&amp;Export...</source>
        <translation>&amp;Exportar...</translation>
    </message>
    <message>
        <location filename="../bitcoingui.cpp" line="210"/>
        <source>Send coins to a Bitcoin address</source>
        <translation type="unfinished"/>
    </message>
    <message>
        <location filename="../bitcoingui.cpp" line="246"/>
        <source>Modify configuration options for Bitcoin</source>
        <translation type="unfinished"/>
    </message>
    <message>
        <location filename="../bitcoingui.cpp" line="249"/>
        <source>Show or hide the Bitcoin window</source>
        <translation>Mostrar ou Ocultar a janela Bitcoin</translation>
    </message>
    <message>
        <location filename="../bitcoingui.cpp" line="251"/>
        <source>Export the data in the current tab to a file</source>
        <translation>Exportar os dados no separador actual para um ficheiro</translation>
    </message>
    <message>
        <location filename="../bitcoingui.cpp" line="253"/>
        <source>Encrypt or decrypt wallet</source>
        <translation>Encriptar ou desencriptar carteira</translation>
    </message>
    <message>
        <location filename="../bitcoingui.cpp" line="256"/>
        <source>Backup wallet to another location</source>
        <translation>Faça uma cópia de segurança da carteira para outra localização</translation>
    </message>
    <message>
        <location filename="../bitcoingui.cpp" line="258"/>
        <source>Change the passphrase used for wallet encryption</source>
        <translation>Mudar a frase de segurança utilizada na encriptação da carteira</translation>
    </message>
    <message>
        <location filename="../bitcoingui.cpp" line="259"/>
        <source>&amp;Debug window</source>
        <translation type="unfinished"/>
    </message>
    <message>
        <location filename="../bitcoingui.cpp" line="260"/>
        <source>Open debugging and diagnostic console</source>
        <translation type="unfinished"/>
    </message>
    <message>
        <location filename="../bitcoingui.cpp" line="261"/>
        <source>&amp;Verify message...</source>
        <translation type="unfinished"/>
    </message>
    <message>
        <location filename="../bitcoingui.cpp" line="262"/>
        <source>Verify a message signature</source>
        <translation type="unfinished"/>
    </message>
    <message>
        <location filename="../bitcoingui.cpp" line="286"/>
        <source>&amp;File</source>
        <translation>&amp;Ficheiro</translation>
    </message>
    <message>
        <location filename="../bitcoingui.cpp" line="296"/>
        <source>&amp;Settings</source>
        <translation>&amp;Configurações</translation>
    </message>
    <message>
        <location filename="../bitcoingui.cpp" line="302"/>
        <source>&amp;Help</source>
        <translation>&amp;Ajuda</translation>
    </message>
    <message>
        <location filename="../bitcoingui.cpp" line="311"/>
        <source>Tabs toolbar</source>
        <translation>Barra de separadores</translation>
    </message>
    <message>
        <location filename="../bitcoingui.cpp" line="322"/>
        <source>Actions toolbar</source>
        <translation>Barra de ações</translation>
    </message>
    <message>
        <location filename="../bitcoingui.cpp" line="334"/>
        <location filename="../bitcoingui.cpp" line="343"/>
        <source>[testnet]</source>
        <translation>[testnet]</translation>
    </message>
    <message>
        <location filename="../bitcoingui.cpp" line="343"/>
        <location filename="../bitcoingui.cpp" line="399"/>
        <source>Bitcoin client</source>
        <translation>Cliente Bitcoin</translation>
    </message>
    <message numerus="yes">
        <location filename="../bitcoingui.cpp" line="492"/>
        <source>%n active connection(s) to Bitcoin network</source>
        <translation><numerusform>%n ligação ativa à rede Bitcoin</numerusform><numerusform>%n ligações ativas à rede Bitcoin</numerusform></translation>
    </message>
    <message>
        <location filename="../bitcoingui.cpp" line="540"/>
        <source>Downloaded %1 blocks of transaction history.</source>
        <translation>Recuperados %1 blocos do histórico de transações.</translation>
    </message>
    <message numerus="yes">
        <location filename="../bitcoingui.cpp" line="555"/>
        <source>%n second(s) ago</source>
        <translation><numerusform>%n segundo</numerusform><numerusform>%n segundos</numerusform></translation>
    </message>
    <message numerus="yes">
        <location filename="../bitcoingui.cpp" line="559"/>
        <source>%n minute(s) ago</source>
        <translation><numerusform>%n minutos</numerusform><numerusform>%n minutos</numerusform></translation>
    </message>
    <message numerus="yes">
        <location filename="../bitcoingui.cpp" line="563"/>
        <source>%n hour(s) ago</source>
        <translation><numerusform>%n hora</numerusform><numerusform>%n horas</numerusform></translation>
    </message>
    <message numerus="yes">
        <location filename="../bitcoingui.cpp" line="567"/>
        <source>%n day(s) ago</source>
        <translation><numerusform>%n dia</numerusform><numerusform>%n dias</numerusform></translation>
    </message>
    <message>
        <location filename="../bitcoingui.cpp" line="573"/>
        <source>Up to date</source>
        <translation>Atualizado</translation>
    </message>
    <message>
        <location filename="../bitcoingui.cpp" line="580"/>
        <source>Catching up...</source>
        <translation>Recuperando...</translation>
    </message>
    <message>
        <location filename="../bitcoingui.cpp" line="590"/>
        <source>Last received block was generated %1.</source>
        <translation>Último bloco recebido foi gerado há %1.</translation>
    </message>
    <message>
        <location filename="../bitcoingui.cpp" line="649"/>
        <source>This transaction is over the size limit.  You can still send it for a fee of %1, which goes to the nodes that process your transaction and helps to support the network.  Do you want to pay the fee?</source>
        <translation>Esta transação tem um tamanho superior ao limite máximo.  Poderá enviá-la pagando uma taxa de %1 que será entregue ao nó que processar a sua transação e ajudará a suportar a rede.  Deseja pagar a taxa?</translation>
    </message>
    <message>
        <location filename="../bitcoingui.cpp" line="654"/>
        <source>Confirm transaction fee</source>
        <translation type="unfinished"/>
    </message>
    <message>
        <location filename="../bitcoingui.cpp" line="681"/>
        <source>Sent transaction</source>
        <translation>Transação enviada</translation>
    </message>
    <message>
        <location filename="../bitcoingui.cpp" line="682"/>
        <source>Incoming transaction</source>
        <translation>Transação recebida</translation>
    </message>
    <message>
        <location filename="../bitcoingui.cpp" line="683"/>
        <source>Date: %1
Amount: %2
Type: %3
Address: %4
</source>
        <translation>Data: %1
Quantidade: %2
Tipo: %3
Endereço: %4</translation>
    </message>
    <message>
        <location filename="../bitcoingui.cpp" line="804"/>
        <source>Wallet is &lt;b&gt;encrypted&lt;/b&gt; and currently &lt;b&gt;unlocked&lt;/b&gt;</source>
        <translation>A carteira está &lt;b&gt;encriptada&lt;/b&gt; e atualmente &lt;b&gt;desbloqueada&lt;/b&gt;</translation>
    </message>
    <message>
        <location filename="../bitcoingui.cpp" line="812"/>
        <source>Wallet is &lt;b&gt;encrypted&lt;/b&gt; and currently &lt;b&gt;locked&lt;/b&gt;</source>
        <translation>A carteira está &lt;b&gt;encriptada&lt;/b&gt; e atualmente &lt;b&gt;bloqueada&lt;/b&gt;</translation>
    </message>
    <message>
        <location filename="../bitcoingui.cpp" line="835"/>
        <source>Backup Wallet</source>
        <translation>Cópia de Segurança da Carteira</translation>
    </message>
    <message>
        <location filename="../bitcoingui.cpp" line="835"/>
        <source>Wallet Data (*.dat)</source>
        <translation>Dados da Carteira (*.dat)</translation>
    </message>
    <message>
        <location filename="../bitcoingui.cpp" line="838"/>
        <source>Backup Failed</source>
        <translation>Cópia de Segurança Falhada</translation>
    </message>
    <message>
        <location filename="../bitcoingui.cpp" line="838"/>
        <source>There was an error trying to save the wallet data to the new location.</source>
        <translation>Ocorreu um erro ao tentar guardar os dados da carteira na nova localização.</translation>
    </message>
    <message>
        <location filename="../bitcoin.cpp" line="112"/>
        <source>A fatal error occured. Bitcoin can no longer continue safely and will quit.</source>
        <translation type="unfinished"/>
    </message>
</context>
<context>
    <name>ClientModel</name>
    <message>
        <location filename="../clientmodel.cpp" line="84"/>
        <source>Network Alert</source>
        <translation type="unfinished"/>
    </message>
</context>
<context>
    <name>DisplayOptionsPage</name>
    <message>
        <location filename="../optionsdialog.cpp" line="246"/>
        <source>Display</source>
        <translation>Janela</translation>
    </message>
    <message>
        <location filename="../optionsdialog.cpp" line="257"/>
        <source>default</source>
        <translation type="unfinished"/>
    </message>
    <message>
        <location filename="../optionsdialog.cpp" line="263"/>
        <source>The user interface language can be set here. This setting will only take effect after restarting Bitcoin.</source>
        <translation type="unfinished"/>
    </message>
    <message>
        <location filename="../optionsdialog.cpp" line="252"/>
        <source>User Interface &amp;Language:</source>
        <translation type="unfinished"/>
    </message>
    <message>
        <location filename="../optionsdialog.cpp" line="273"/>
        <source>&amp;Unit to show amounts in:</source>
        <translation type="unfinished"/>
    </message>
    <message>
        <location filename="../optionsdialog.cpp" line="277"/>
        <source>Choose the default subdivision unit to show in the interface, and when sending coins</source>
        <translation>Escolha a subdivisão unitária a ser mostrada por defeito na aplicação e ao enviar moedas</translation>
    </message>
    <message>
        <location filename="../optionsdialog.cpp" line="284"/>
        <source>&amp;Display addresses in transaction list</source>
        <translation type="unfinished"/>
    </message>
    <message>
        <location filename="../optionsdialog.cpp" line="285"/>
        <source>Whether to show Bitcoin addresses in the transaction list</source>
        <translation type="unfinished"/>
    </message>
    <message>
        <location filename="../optionsdialog.cpp" line="303"/>
        <source>Warning</source>
        <translation type="unfinished"/>
    </message>
    <message>
        <location filename="../optionsdialog.cpp" line="303"/>
        <source>This setting will take effect after restarting Bitcoin.</source>
        <translation type="unfinished"/>
    </message>
</context>
<context>
    <name>EditAddressDialog</name>
    <message>
        <location filename="../forms/editaddressdialog.ui" line="14"/>
        <source>Edit Address</source>
        <translation>Editar Endereço</translation>
    </message>
    <message>
        <location filename="../forms/editaddressdialog.ui" line="25"/>
        <source>&amp;Label</source>
        <translation>&amp;Rótulo</translation>
    </message>
    <message>
        <location filename="../forms/editaddressdialog.ui" line="35"/>
        <source>The label associated with this address book entry</source>
        <translation>O rótulo a ser associado com esta entrada do livro de endereços</translation>
    </message>
    <message>
        <location filename="../forms/editaddressdialog.ui" line="42"/>
        <source>&amp;Address</source>
        <translation>&amp;Endereço</translation>
    </message>
    <message>
        <location filename="../forms/editaddressdialog.ui" line="52"/>
        <source>The address associated with this address book entry. This can only be modified for sending addresses.</source>
        <translation>O endereço associado com esta entrada do livro de endereços. Apenas poderá ser modificado para endereços de saída.</translation>
    </message>
    <message>
        <location filename="../editaddressdialog.cpp" line="20"/>
        <source>New receiving address</source>
        <translation>Novo endereço de entrada</translation>
    </message>
    <message>
        <location filename="../editaddressdialog.cpp" line="24"/>
        <source>New sending address</source>
        <translation>Novo endereço de saída</translation>
    </message>
    <message>
        <location filename="../editaddressdialog.cpp" line="27"/>
        <source>Edit receiving address</source>
        <translation>&amp;Ajuda</translation>
    </message>
    <message>
        <location filename="../editaddressdialog.cpp" line="31"/>
        <source>Edit sending address</source>
        <translation>Editar endereço de saída</translation>
    </message>
    <message>
        <location filename="../editaddressdialog.cpp" line="91"/>
        <source>The entered address &quot;%1&quot; is already in the address book.</source>
        <translation>O endereço introduzido &quot;%1&quot; já se encontra no livro de endereços.</translation>
    </message>
    <message>
        <location filename="../editaddressdialog.cpp" line="96"/>
        <source>The entered address &quot;%1&quot; is not a valid Bitcoin address.</source>
        <translation type="unfinished"/>
    </message>
    <message>
        <location filename="../editaddressdialog.cpp" line="101"/>
        <source>Could not unlock wallet.</source>
        <translation>Impossível desbloquear carteira.</translation>
    </message>
    <message>
        <location filename="../editaddressdialog.cpp" line="106"/>
        <source>New key generation failed.</source>
        <translation>Falha ao gerar nova chave.</translation>
    </message>
</context>
<context>
    <name>HelpMessageBox</name>
    <message>
        <location filename="../bitcoin.cpp" line="133"/>
        <location filename="../bitcoin.cpp" line="143"/>
        <source>Bitcoin-Qt</source>
        <translation type="unfinished"/>
    </message>
    <message>
        <location filename="../bitcoin.cpp" line="133"/>
        <source>version</source>
        <translation type="unfinished"/>
    </message>
    <message>
        <location filename="../bitcoin.cpp" line="135"/>
        <source>Usage:</source>
        <translation>Utilização:</translation>
    </message>
    <message>
        <location filename="../bitcoin.cpp" line="136"/>
        <source>options</source>
        <translation type="unfinished"/>
    </message>
    <message>
        <location filename="../bitcoin.cpp" line="138"/>
        <source>UI options</source>
        <translation type="unfinished"/>
    </message>
    <message>
        <location filename="../bitcoin.cpp" line="139"/>
        <source>Set language, for example &quot;de_DE&quot; (default: system locale)</source>
        <translation>Definir linguagem, por exemplo &quot;pt_PT&quot; (por defeito: linguagem do sistema)</translation>
    </message>
    <message>
        <location filename="../bitcoin.cpp" line="140"/>
        <source>Start minimized</source>
        <translation>Iniciar minimizado</translation>
    </message>
    <message>
        <location filename="../bitcoin.cpp" line="141"/>
        <source>Show splash screen on startup (default: 1)</source>
        <translation>Mostrar animação ao iniciar (por defeito: 1)</translation>
    </message>
</context>
<context>
    <name>MainOptionsPage</name>
    <message>
        <location filename="../optionsdialog.cpp" line="227"/>
        <source>Detach block and address databases at shutdown. This means they can be moved to another data directory, but it slows down shutdown. The wallet is always detached.</source>
        <translation type="unfinished"/>
    </message>
    <message>
        <location filename="../optionsdialog.cpp" line="212"/>
        <source>Pay transaction &amp;fee</source>
        <translation>Pagar &amp;taxa de transação</translation>
    </message>
    <message>
        <location filename="../optionsdialog.cpp" line="204"/>
        <source>Main</source>
        <translation>Geral</translation>
    </message>
    <message>
        <location filename="../optionsdialog.cpp" line="206"/>
        <source>Optional transaction fee per kB that helps make sure your transactions are processed quickly. Most transactions are 1 kB. Fee 0.01 recommended.</source>
        <translation>Taxa de transação opcional por kB que ajuda a assegurar que as suas transações serão processadas rapidamente. A maioria das transações tem 1 kB. Taxa de 0.01 recomendada.</translation>
    </message>
    <message>
        <location filename="../optionsdialog.cpp" line="222"/>
        <source>&amp;Start Bitcoin on system login</source>
        <translation type="unfinished"/>
    </message>
    <message>
        <location filename="../optionsdialog.cpp" line="223"/>
        <source>Automatically start Bitcoin after logging in to the system</source>
        <translation type="unfinished"/>
    </message>
    <message>
        <location filename="../optionsdialog.cpp" line="226"/>
        <source>&amp;Detach databases at shutdown</source>
        <translation type="unfinished"/>
    </message>
</context>
<context>
    <name>MessagePage</name>
    <message>
        <location filename="../forms/messagepage.ui" line="14"/>
        <source>Sign Message</source>
        <translation type="unfinished"/>
    </message>
    <message>
        <location filename="../forms/messagepage.ui" line="20"/>
        <source>You can sign messages with your addresses to prove you own them. Be careful not to sign anything vague, as phishing attacks may try to trick you into signing your identity over to them. Only sign fully-detailed statements you agree to.</source>
        <translation>Pode assinar mensagens com os seus endereços para provar que são seus. Tenha atenção ao assinar mensagens ambíguas, pois ataques de phishing podem tentar enganá-lo, de modo a assinar a sua identidade para os atacantes. Apenas assine declarações completamente detalhadas com as quais concorde.</translation>
    </message>
    <message>
        <location filename="../forms/messagepage.ui" line="38"/>
        <source>The address to sign the message with  (e.g. **********************************)</source>
        <translation>O endereço a utilizar para assinar a mensagem  (p.ex. **********************************)</translation>
    </message>
    <message>
        <location filename="../forms/messagepage.ui" line="48"/>
        <source>Choose adress from address book</source>
        <translation>Escolher endereço </translation>
    </message>
    <message>
        <location filename="../forms/messagepage.ui" line="58"/>
        <source>Alt+A</source>
        <translation>Alt+A</translation>
    </message>
    <message>
        <location filename="../forms/messagepage.ui" line="71"/>
        <source>Paste address from clipboard</source>
        <translation>Paste address from clipboard</translation>
    </message>
    <message>
        <location filename="../forms/messagepage.ui" line="81"/>
        <source>Alt+P</source>
        <translation>Alt+P</translation>
    </message>
    <message>
        <location filename="../forms/messagepage.ui" line="93"/>
        <source>Enter the message you want to sign here</source>
        <translation>Escreva aqui a mensagem que deseja assinar</translation>
    </message>
    <message>
        <location filename="../forms/messagepage.ui" line="128"/>
        <source>Copy the current signature to the system clipboard</source>
        <translation type="unfinished"/>
    </message>
    <message>
        <location filename="../forms/messagepage.ui" line="131"/>
        <source>&amp;Copy Signature</source>
        <translation type="unfinished"/>
    </message>
    <message>
        <location filename="../forms/messagepage.ui" line="142"/>
        <source>Reset all sign message fields</source>
        <translation type="unfinished"/>
    </message>
    <message>
        <location filename="../forms/messagepage.ui" line="145"/>
        <source>Clear &amp;All</source>
        <translation type="unfinished"/>
    </message>
    <message>
        <location filename="../messagepage.cpp" line="31"/>
        <source>Click &quot;Sign Message&quot; to get signature</source>
        <translation>Clique &quot;Assinar mensagem&quot; para aceder á assinatura</translation>
    </message>
    <message>
        <location filename="../forms/messagepage.ui" line="114"/>
        <source>Sign a message to prove you own this address</source>
        <translation>Assine uma mensagem para provar que é dono deste endereço</translation>
    </message>
    <message>
        <location filename="../forms/messagepage.ui" line="117"/>
        <source>&amp;Sign Message</source>
        <translation>&amp;Assinar Mensagem</translation>
    </message>
    <message>
        <location filename="../messagepage.cpp" line="30"/>
        <source>Enter a Bitcoin address (e.g. **********************************)</source>
        <translation>Introduza um endereço Bitcoin (p.ex. **********************************)</translation>
    </message>
    <message>
        <location filename="../messagepage.cpp" line="83"/>
        <location filename="../messagepage.cpp" line="90"/>
        <location filename="../messagepage.cpp" line="105"/>
        <location filename="../messagepage.cpp" line="117"/>
        <source>Error signing</source>
        <translation>Erro ao assinar</translation>
    </message>
    <message>
        <location filename="../messagepage.cpp" line="83"/>
        <source>%1 is not a valid address.</source>
        <translation>%1 não é um endereço válido.</translation>
    </message>
    <message>
        <location filename="../messagepage.cpp" line="90"/>
        <source>%1 does not refer to a key.</source>
        <translation type="unfinished"/>
    </message>
    <message>
        <location filename="../messagepage.cpp" line="105"/>
        <source>Private key for %1 is not available.</source>
        <translation>A chave privada para %1 não está disponível.</translation>
    </message>
    <message>
        <location filename="../messagepage.cpp" line="117"/>
        <source>Sign failed</source>
        <translation>Falha ao assinar</translation>
    </message>
</context>
<context>
    <name>NetworkOptionsPage</name>
    <message>
        <location filename="../optionsdialog.cpp" line="345"/>
        <source>Network</source>
        <translation type="unfinished"/>
    </message>
    <message>
        <location filename="../optionsdialog.cpp" line="347"/>
        <source>Map port using &amp;UPnP</source>
        <translation>Mapear porta usando &amp;UPnP</translation>
    </message>
    <message>
        <location filename="../optionsdialog.cpp" line="348"/>
        <source>Automatically open the Bitcoin client port on the router. This only works when your router supports UPnP and it is enabled.</source>
        <translation>Abrir a porta do cliente bitcoin automaticamente no seu router. Isto penas funciona se o seu router suportar UPnP e este se encontrar ligado.</translation>
    </message>
    <message>
        <location filename="../optionsdialog.cpp" line="351"/>
        <source>&amp;Connect through SOCKS4 proxy:</source>
        <translation>&amp;Ligar através de proxy SOCKS4:</translation>
    </message>
    <message>
        <location filename="../optionsdialog.cpp" line="352"/>
        <source>Connect to the Bitcon network through a SOCKS4 proxy (e.g. when connecting through Tor)</source>
        <translation>Ligar à rede Bitcoin através de um proxy SOCKS4 (p.ex. quando ligar através de Tor)</translation>
    </message>
    <message>
        <location filename="../optionsdialog.cpp" line="357"/>
        <source>Proxy &amp;IP:</source>
        <translation type="unfinished"/>
    </message>
    <message>
        <location filename="../optionsdialog.cpp" line="366"/>
        <source>&amp;Port:</source>
        <translation type="unfinished"/>
    </message>
    <message>
        <location filename="../optionsdialog.cpp" line="363"/>
        <source>IP address of the proxy (e.g. 127.0.0.1)</source>
        <translation>Endereço IP do proxy (p.ex. 127.0.0.1)</translation>
    </message>
    <message>
        <location filename="../optionsdialog.cpp" line="372"/>
        <source>Port of the proxy (e.g. 1234)</source>
        <translation>Porta do proxy (p.ex. 1234)</translation>
    </message>
</context>
<context>
    <name>OptionsDialog</name>
    <message>
        <location filename="../optionsdialog.cpp" line="135"/>
        <source>Options</source>
        <translation>Opções</translation>
    </message>
</context>
<context>
    <name>OverviewPage</name>
    <message>
        <location filename="../forms/overviewpage.ui" line="14"/>
        <source>Form</source>
        <translation>Formulário</translation>
    </message>
    <message>
        <location filename="../forms/overviewpage.ui" line="47"/>
        <location filename="../forms/overviewpage.ui" line="204"/>
        <source>The displayed information may be out of date. Your wallet automatically synchronizes with the Bitcoin network after a connection is established, but this process has not completed yet.</source>
        <translation type="unfinished"/>
    </message>
    <message>
        <location filename="../forms/overviewpage.ui" line="89"/>
        <source>Balance:</source>
        <translation>Saldo:</translation>
    </message>
    <message>
        <location filename="../forms/overviewpage.ui" line="147"/>
        <source>Number of transactions:</source>
        <translation>Número de transações:</translation>
    </message>
    <message>
        <location filename="../forms/overviewpage.ui" line="118"/>
        <source>Unconfirmed:</source>
        <translation>Não confirmado:</translation>
    </message>
    <message>
        <location filename="../forms/overviewpage.ui" line="40"/>
        <source>Wallet</source>
        <translation type="unfinished"/>
    </message>
    <message>
        <location filename="../forms/overviewpage.ui" line="197"/>
        <source>&lt;b&gt;Recent transactions&lt;/b&gt;</source>
        <translation>&lt;b&gt;Transações recentes&lt;/b&gt;</translation>
    </message>
    <message>
        <location filename="../forms/overviewpage.ui" line="105"/>
        <source>Your current balance</source>
        <translation>O seu saldo actual</translation>
    </message>
    <message>
        <location filename="../forms/overviewpage.ui" line="134"/>
        <source>Total of transactions that have yet to be confirmed, and do not yet count toward the current balance</source>
        <translation>Total de transações ainda não confirmadas, e que não estão contabilizadas ainda no seu saldo actual</translation>
    </message>
    <message>
        <location filename="../forms/overviewpage.ui" line="154"/>
        <source>Total number of transactions in wallet</source>
        <translation>Número total de transações na carteira</translation>
    </message>
    <message>
        <location filename="../overviewpage.cpp" line="110"/>
        <location filename="../overviewpage.cpp" line="111"/>
        <source>out of sync</source>
        <translation type="unfinished"/>
    </message>
</context>
<context>
    <name>QRCodeDialog</name>
    <message>
        <location filename="../forms/qrcodedialog.ui" line="14"/>
        <source>QR Code Dialog</source>
        <translation type="unfinished"/>
    </message>
    <message>
        <location filename="../forms/qrcodedialog.ui" line="32"/>
        <source>QR Code</source>
        <translation>Código QR</translation>
    </message>
    <message>
        <location filename="../forms/qrcodedialog.ui" line="55"/>
        <source>Request Payment</source>
        <translation>Requisitar Pagamento</translation>
    </message>
    <message>
        <location filename="../forms/qrcodedialog.ui" line="70"/>
        <source>Amount:</source>
        <translation>Quantia:</translation>
    </message>
    <message>
        <location filename="../forms/qrcodedialog.ui" line="105"/>
        <source>BTC</source>
        <translation>BTC</translation>
    </message>
    <message>
        <location filename="../forms/qrcodedialog.ui" line="121"/>
        <source>Label:</source>
        <translation>Rótulo:</translation>
    </message>
    <message>
        <location filename="../forms/qrcodedialog.ui" line="144"/>
        <source>Message:</source>
        <translation>Message:</translation>
    </message>
    <message>
        <location filename="../forms/qrcodedialog.ui" line="186"/>
        <source>&amp;Save As...</source>
        <translation>&amp;Salvar Como...</translation>
    </message>
    <message>
        <location filename="../qrcodedialog.cpp" line="45"/>
        <source>Error encoding URI into QR Code.</source>
        <translation type="unfinished"/>
    </message>
    <message>
        <location filename="../qrcodedialog.cpp" line="63"/>
        <source>Resulting URI too long, try to reduce the text for label / message.</source>
        <translation>URI resultante muito longo. Tente reduzir o texto do rótulo / mensagem.</translation>
    </message>
    <message>
        <location filename="../qrcodedialog.cpp" line="120"/>
        <source>Save QR Code</source>
        <translation type="unfinished"/>
    </message>
    <message>
        <location filename="../qrcodedialog.cpp" line="120"/>
        <source>PNG Images (*.png)</source>
        <translation>Imagens PNG (*.png)</translation>
    </message>
</context>
<context>
    <name>RPCConsole</name>
    <message>
        <location filename="../forms/rpcconsole.ui" line="14"/>
        <source>Bitcoin debug window</source>
        <translation type="unfinished"/>
    </message>
    <message>
        <location filename="../forms/rpcconsole.ui" line="46"/>
        <source>Client name</source>
        <translation type="unfinished"/>
    </message>
    <message>
        <location filename="../forms/rpcconsole.ui" line="56"/>
        <location filename="../forms/rpcconsole.ui" line="79"/>
        <location filename="../forms/rpcconsole.ui" line="102"/>
        <location filename="../forms/rpcconsole.ui" line="125"/>
        <location filename="../forms/rpcconsole.ui" line="161"/>
        <location filename="../forms/rpcconsole.ui" line="214"/>
        <location filename="../forms/rpcconsole.ui" line="237"/>
        <location filename="../forms/rpcconsole.ui" line="260"/>
        <location filename="../rpcconsole.cpp" line="245"/>
        <source>N/A</source>
        <translation type="unfinished"/>
    </message>
    <message>
        <location filename="../forms/rpcconsole.ui" line="69"/>
        <source>Client version</source>
        <translation type="unfinished"/>
    </message>
    <message>
        <location filename="../forms/rpcconsole.ui" line="24"/>
        <source>&amp;Information</source>
        <translation type="unfinished"/>
    </message>
    <message>
        <location filename="../forms/rpcconsole.ui" line="39"/>
        <source>Client</source>
        <translation type="unfinished"/>
    </message>
    <message>
        <location filename="../forms/rpcconsole.ui" line="115"/>
        <source>Startup time</source>
        <translation type="unfinished"/>
    </message>
    <message>
        <location filename="../forms/rpcconsole.ui" line="144"/>
        <source>Network</source>
        <translation type="unfinished"/>
    </message>
    <message>
        <location filename="../forms/rpcconsole.ui" line="151"/>
        <source>Number of connections</source>
        <translation type="unfinished"/>
    </message>
    <message>
        <location filename="../forms/rpcconsole.ui" line="174"/>
        <source>On testnet</source>
        <translation type="unfinished"/>
    </message>
    <message>
        <location filename="../forms/rpcconsole.ui" line="197"/>
        <source>Block chain</source>
        <translation type="unfinished"/>
    </message>
    <message>
        <location filename="../forms/rpcconsole.ui" line="204"/>
        <source>Current number of blocks</source>
        <translation type="unfinished"/>
    </message>
    <message>
        <location filename="../forms/rpcconsole.ui" line="227"/>
        <source>Estimated total blocks</source>
        <translation type="unfinished"/>
    </message>
    <message>
        <location filename="../forms/rpcconsole.ui" line="250"/>
        <source>Last block time</source>
        <translation type="unfinished"/>
    </message>
    <message>
        <location filename="../forms/rpcconsole.ui" line="292"/>
        <source>Debug logfile</source>
        <translation type="unfinished"/>
    </message>
    <message>
        <location filename="../forms/rpcconsole.ui" line="299"/>
        <source>Open the Bitcoin debug logfile from the current data directory. This can take a few seconds for large logfiles.</source>
        <translation type="unfinished"/>
    </message>
    <message>
        <location filename="../forms/rpcconsole.ui" line="302"/>
        <source>&amp;Open</source>
        <translation type="unfinished"/>
    </message>
    <message>
        <location filename="../forms/rpcconsole.ui" line="323"/>
        <source>&amp;Console</source>
        <translation type="unfinished"/>
    </message>
    <message>
        <location filename="../forms/rpcconsole.ui" line="92"/>
        <source>Build date</source>
        <translation type="unfinished"/>
    </message>
    <message>
        <location filename="../forms/rpcconsole.ui" line="372"/>
        <source>Clear console</source>
        <translation type="unfinished"/>
    </message>
    <message>
        <location filename="../rpcconsole.cpp" line="212"/>
        <source>Welcome to the Bitcoin RPC console.</source>
        <translation type="unfinished"/>
    </message>
    <message>
        <location filename="../rpcconsole.cpp" line="213"/>
        <source>Use up and down arrows to navigate history, and &lt;b&gt;Ctrl-L&lt;/b&gt; to clear screen.</source>
        <translation type="unfinished"/>
    </message>
    <message>
        <location filename="../rpcconsole.cpp" line="214"/>
        <source>Type &lt;b&gt;help&lt;/b&gt; for an overview of available commands.</source>
        <translation type="unfinished"/>
    </message>
</context>
<context>
    <name>SendCoinsDialog</name>
    <message>
        <location filename="../forms/sendcoinsdialog.ui" line="14"/>
        <location filename="../sendcoinsdialog.cpp" line="122"/>
        <location filename="../sendcoinsdialog.cpp" line="127"/>
        <location filename="../sendcoinsdialog.cpp" line="132"/>
        <location filename="../sendcoinsdialog.cpp" line="137"/>
        <location filename="../sendcoinsdialog.cpp" line="143"/>
        <location filename="../sendcoinsdialog.cpp" line="148"/>
        <location filename="../sendcoinsdialog.cpp" line="153"/>
        <source>Send Coins</source>
        <translation>Enviar Moedas</translation>
    </message>
    <message>
        <location filename="../forms/sendcoinsdialog.ui" line="64"/>
        <source>Send to multiple recipients at once</source>
        <translation>Enviar para múltiplos destinatários de uma vez</translation>
    </message>
    <message>
        <location filename="../forms/sendcoinsdialog.ui" line="67"/>
        <source>&amp;Add Recipient</source>
        <translation type="unfinished"/>
    </message>
    <message>
        <location filename="../forms/sendcoinsdialog.ui" line="84"/>
        <source>Remove all transaction fields</source>
        <translation>Remover todos os campos da transação</translation>
    </message>
    <message>
        <location filename="../forms/sendcoinsdialog.ui" line="87"/>
        <source>Clear &amp;All</source>
        <translation type="unfinished"/>
    </message>
    <message>
        <location filename="../forms/sendcoinsdialog.ui" line="106"/>
        <source>Balance:</source>
        <translation>Saldo:</translation>
    </message>
    <message>
        <location filename="../forms/sendcoinsdialog.ui" line="113"/>
        <source>123.456 BTC</source>
        <translation>123.456 BTC</translation>
    </message>
    <message>
        <location filename="../forms/sendcoinsdialog.ui" line="144"/>
        <source>Confirm the send action</source>
        <translation>Confirme ação de envio</translation>
    </message>
    <message>
        <location filename="../forms/sendcoinsdialog.ui" line="147"/>
        <source>&amp;Send</source>
        <translation>&amp;Enviar</translation>
    </message>
    <message>
        <location filename="../sendcoinsdialog.cpp" line="94"/>
        <source>&lt;b&gt;%1&lt;/b&gt; to %2 (%3)</source>
        <translation>&lt;b&gt;%1&lt;/b&gt; para %2 (%3)</translation>
    </message>
    <message>
        <location filename="../sendcoinsdialog.cpp" line="99"/>
        <source>Confirm send coins</source>
        <translation>Confirme envio de moedas</translation>
    </message>
    <message>
        <location filename="../sendcoinsdialog.cpp" line="100"/>
        <source>Are you sure you want to send %1?</source>
        <translation>Tem a certeza que deseja enviar %1?</translation>
    </message>
    <message>
        <location filename="../sendcoinsdialog.cpp" line="100"/>
        <source> and </source>
        <translation> e </translation>
    </message>
    <message>
        <location filename="../sendcoinsdialog.cpp" line="123"/>
        <source>The recepient address is not valid, please recheck.</source>
        <translation>O endereço de destino não é válido, por favor verifique.</translation>
    </message>
    <message>
        <location filename="../sendcoinsdialog.cpp" line="128"/>
        <source>The amount to pay must be larger than 0.</source>
        <translation>A quantia a pagar deverá ser maior que 0.</translation>
    </message>
    <message>
        <location filename="../sendcoinsdialog.cpp" line="133"/>
        <source>The amount exceeds your balance.</source>
        <translation type="unfinished"/>
    </message>
    <message>
        <location filename="../sendcoinsdialog.cpp" line="138"/>
        <source>The total exceeds your balance when the %1 transaction fee is included.</source>
        <translation type="unfinished"/>
    </message>
    <message>
        <location filename="../sendcoinsdialog.cpp" line="144"/>
        <source>Duplicate address found, can only send to each address once per send operation.</source>
        <translation type="unfinished"/>
    </message>
    <message>
        <location filename="../sendcoinsdialog.cpp" line="149"/>
        <source>Error: Transaction creation failed.</source>
        <translation type="unfinished"/>
    </message>
    <message>
        <location filename="../sendcoinsdialog.cpp" line="154"/>
        <source>Error: The transaction was rejected. This might happen if some of the coins in your wallet were already spent, such as if you used a copy of wallet.dat and coins were spent in the copy but not marked as spent here.</source>
        <translation type="unfinished"/>
    </message>
</context>
<context>
    <name>SendCoinsEntry</name>
    <message>
        <location filename="../forms/sendcoinsentry.ui" line="14"/>
        <source>Form</source>
        <translation>Formulário</translation>
    </message>
    <message>
        <location filename="../forms/sendcoinsentry.ui" line="29"/>
        <source>A&amp;mount:</source>
        <translation>Q&amp;uantia:</translation>
    </message>
    <message>
        <location filename="../forms/sendcoinsentry.ui" line="42"/>
        <source>Pay &amp;To:</source>
        <translation>Pagar &amp;A:</translation>
    </message>
    <message>
        <location filename="../forms/sendcoinsentry.ui" line="66"/>
        <location filename="../sendcoinsentry.cpp" line="25"/>
        <source>Enter a label for this address to add it to your address book</source>
        <translation>Escreva um rótulo para este endereço para o adicionar ao seu livro de endereços</translation>
    </message>
    <message>
        <location filename="../forms/sendcoinsentry.ui" line="75"/>
        <source>&amp;Label:</source>
        <translation>&amp;Rótulo:</translation>
    </message>
    <message>
        <location filename="../forms/sendcoinsentry.ui" line="93"/>
        <source>The address to send the payment to  (e.g. **********************************)</source>
        <translation>O endereço para onde enviar o pagamento  (p.ex. **********************************)</translation>
    </message>
    <message>
        <location filename="../forms/sendcoinsentry.ui" line="103"/>
        <source>Choose address from address book</source>
        <translation>Escolher endereço do livro de endereços</translation>
    </message>
    <message>
        <location filename="../forms/sendcoinsentry.ui" line="113"/>
        <source>Alt+A</source>
        <translation>Alt+A</translation>
    </message>
    <message>
        <location filename="../forms/sendcoinsentry.ui" line="120"/>
        <source>Paste address from clipboard</source>
        <translation>Cole endereço da área de transferência</translation>
    </message>
    <message>
        <location filename="../forms/sendcoinsentry.ui" line="130"/>
        <source>Alt+P</source>
        <translation>Alt+P</translation>
    </message>
    <message>
        <location filename="../forms/sendcoinsentry.ui" line="137"/>
        <source>Remove this recipient</source>
        <translation>Remover este destinatário</translation>
    </message>
    <message>
        <location filename="../sendcoinsentry.cpp" line="26"/>
        <source>Enter a Bitcoin address (e.g. **********************************)</source>
        <translation>Introduza um endereço Bitcoin (p.ex. **********************************)</translation>
    </message>
</context>
<context>
    <name>TransactionDesc</name>
    <message>
        <location filename="../transactiondesc.cpp" line="21"/>
        <source>Open for %1 blocks</source>
        <translation>Aberto para %1 blocos</translation>
    </message>
    <message>
        <location filename="../transactiondesc.cpp" line="23"/>
        <source>Open until %1</source>
        <translation>Aberto até %1</translation>
    </message>
    <message>
        <location filename="../transactiondesc.cpp" line="29"/>
        <source>%1/offline?</source>
        <translation>%1/desligado?</translation>
    </message>
    <message>
        <location filename="../transactiondesc.cpp" line="31"/>
        <source>%1/unconfirmed</source>
        <translation>%1/não confirmada</translation>
    </message>
    <message>
        <location filename="../transactiondesc.cpp" line="33"/>
        <source>%1 confirmations</source>
        <translation>%1 confirmações</translation>
    </message>
    <message>
        <location filename="../transactiondesc.cpp" line="51"/>
        <source>&lt;b&gt;Status:&lt;/b&gt; </source>
        <translation>&lt;b&gt;Estado:&lt;/b&gt; </translation>
    </message>
    <message>
        <location filename="../transactiondesc.cpp" line="56"/>
        <source>, has not been successfully broadcast yet</source>
        <translation>, ainda não foi transmitida com sucesso</translation>
    </message>
    <message>
        <location filename="../transactiondesc.cpp" line="58"/>
        <source>, broadcast through %1 node</source>
        <translation>, transmitida através de %1 nó</translation>
    </message>
    <message>
        <location filename="../transactiondesc.cpp" line="60"/>
        <source>, broadcast through %1 nodes</source>
        <translation>, transmitida através de %1 nós</translation>
    </message>
    <message>
        <location filename="../transactiondesc.cpp" line="64"/>
        <source>&lt;b&gt;Date:&lt;/b&gt; </source>
        <translation>&lt;b&gt;Data:&lt;/b&gt; </translation>
    </message>
    <message>
        <location filename="../transactiondesc.cpp" line="71"/>
        <source>&lt;b&gt;Source:&lt;/b&gt; Generated&lt;br&gt;</source>
        <translation>&lt;b&gt;Fonte:&lt;/b&gt; Geradas&lt;br&gt;</translation>
    </message>
    <message>
        <location filename="../transactiondesc.cpp" line="77"/>
        <location filename="../transactiondesc.cpp" line="94"/>
        <source>&lt;b&gt;From:&lt;/b&gt; </source>
        <translation>&lt;b&gt;De:&lt;/b&gt; </translation>
    </message>
    <message>
        <location filename="../transactiondesc.cpp" line="94"/>
        <source>unknown</source>
        <translation>desconhecido</translation>
    </message>
    <message>
        <location filename="../transactiondesc.cpp" line="95"/>
        <location filename="../transactiondesc.cpp" line="118"/>
        <location filename="../transactiondesc.cpp" line="178"/>
        <source>&lt;b&gt;To:&lt;/b&gt; </source>
        <translation>&lt;b&gt;Para:&lt;/b&gt; </translation>
    </message>
    <message>
        <location filename="../transactiondesc.cpp" line="98"/>
        <source> (yours, label: </source>
        <translation> (seu, rótulo: </translation>
    </message>
    <message>
        <location filename="../transactiondesc.cpp" line="100"/>
        <source> (yours)</source>
        <translation> (seu)</translation>
    </message>
    <message>
        <location filename="../transactiondesc.cpp" line="136"/>
        <location filename="../transactiondesc.cpp" line="150"/>
        <location filename="../transactiondesc.cpp" line="195"/>
        <location filename="../transactiondesc.cpp" line="212"/>
        <source>&lt;b&gt;Credit:&lt;/b&gt; </source>
        <translation>&lt;b&gt;Crédito:&lt;/b&gt; </translation>
    </message>
    <message>
        <location filename="../transactiondesc.cpp" line="138"/>
        <source>(%1 matures in %2 more blocks)</source>
        <translation>(%1 matura daqui por %2 blocos)</translation>
    </message>
    <message>
        <location filename="../transactiondesc.cpp" line="142"/>
        <source>(not accepted)</source>
        <translation>(não aceite)</translation>
    </message>
    <message>
        <location filename="../transactiondesc.cpp" line="186"/>
        <location filename="../transactiondesc.cpp" line="194"/>
        <location filename="../transactiondesc.cpp" line="209"/>
        <source>&lt;b&gt;Debit:&lt;/b&gt; </source>
        <translation>&lt;b&gt;Débito:&lt;/b&gt; </translation>
    </message>
    <message>
        <location filename="../transactiondesc.cpp" line="200"/>
        <source>&lt;b&gt;Transaction fee:&lt;/b&gt; </source>
        <translation>&lt;b&gt;Taxa de transação:&lt;/b&gt; </translation>
    </message>
    <message>
        <location filename="../transactiondesc.cpp" line="216"/>
        <source>&lt;b&gt;Net amount:&lt;/b&gt; </source>
        <translation>&lt;b&gt;Valor líquido:&lt;/b&gt; </translation>
    </message>
    <message>
        <location filename="../transactiondesc.cpp" line="222"/>
        <source>Message:</source>
        <translation>Mensagem:</translation>
    </message>
    <message>
        <location filename="../transactiondesc.cpp" line="224"/>
        <source>Comment:</source>
        <translation>Comentário:</translation>
    </message>
    <message>
        <location filename="../transactiondesc.cpp" line="226"/>
        <source>Transaction ID:</source>
        <translation>ID da Transação:</translation>
    </message>
    <message>
        <location filename="../transactiondesc.cpp" line="229"/>
        <source>Generated coins must wait 120 blocks before they can be spent.  When you generated this block, it was broadcast to the network to be added to the block chain.  If it fails to get into the chain, it will change to &quot;not accepted&quot; and not be spendable.  This may occasionally happen if another node generates a block within a few seconds of yours.</source>
        <translation>Moedas geradas deverão maturar por 120 blocos antes de poderem ser gastas.  Quando gerou este bloco, ele foi transmitido para a rede para ser incluído na cadeia de blocos.  Se a inclusão na cadeia de blocos falhar, irá mudar o estado para &quot;não aceite&quot; e as moedas não poderão ser gastas.  Isto poderá acontecer ocasionalmente se outro nó da rede gerar um bloco a poucos segundos de diferença do seu.</translation>
    </message>
</context>
<context>
    <name>TransactionDescDialog</name>
    <message>
        <location filename="../forms/transactiondescdialog.ui" line="14"/>
        <source>Transaction details</source>
        <translation>Detalhes da transação</translation>
    </message>
    <message>
        <location filename="../forms/transactiondescdialog.ui" line="20"/>
        <source>This pane shows a detailed description of the transaction</source>
        <translation>Esta janela mostra uma descrição detalhada da transação</translation>
    </message>
</context>
<context>
    <name>TransactionTableModel</name>
    <message>
        <location filename="../transactiontablemodel.cpp" line="226"/>
        <source>Date</source>
        <translation>Data</translation>
    </message>
    <message>
        <location filename="../transactiontablemodel.cpp" line="226"/>
        <source>Type</source>
        <translation>Tipo</translation>
    </message>
    <message>
        <location filename="../transactiontablemodel.cpp" line="226"/>
        <source>Address</source>
        <translation>Endereço</translation>
    </message>
    <message>
        <location filename="../transactiontablemodel.cpp" line="226"/>
        <source>Amount</source>
        <translation>Quantia</translation>
    </message>
    <message numerus="yes">
        <location filename="../transactiontablemodel.cpp" line="281"/>
        <source>Open for %n block(s)</source>
        <translation><numerusform>Aberto para %n bloco</numerusform><numerusform>Aberto para %n blocos</numerusform></translation>
    </message>
    <message>
        <location filename="../transactiontablemodel.cpp" line="284"/>
        <source>Open until %1</source>
        <translation>Aberto até %1</translation>
    </message>
    <message>
        <location filename="../transactiontablemodel.cpp" line="287"/>
        <source>Offline (%1 confirmations)</source>
        <translation>Desligado (%1 confirmação)</translation>
    </message>
    <message>
        <location filename="../transactiontablemodel.cpp" line="290"/>
        <source>Unconfirmed (%1 of %2 confirmations)</source>
        <translation>Não confirmada (%1 de %2 confirmações)</translation>
    </message>
    <message>
        <location filename="../transactiontablemodel.cpp" line="293"/>
        <source>Confirmed (%1 confirmations)</source>
        <translation>Confirmada (%1 confirmação)</translation>
    </message>
    <message numerus="yes">
        <location filename="../transactiontablemodel.cpp" line="301"/>
        <source>Mined balance will be available in %n more blocks</source>
        <translation><numerusform>Saldo minado estará disponível daqui por %n bloco</numerusform><numerusform>Saldo minado estará disponível daqui por %n blocos</numerusform></translation>
    </message>
    <message>
        <location filename="../transactiontablemodel.cpp" line="307"/>
        <source>This block was not received by any other nodes and will probably not be accepted!</source>
        <translation>Este bloco não foi recebido por outros nós e provavelmente não será aceite pela rede!</translation>
    </message>
    <message>
        <location filename="../transactiontablemodel.cpp" line="310"/>
        <source>Generated but not accepted</source>
        <translation>Gerado mas não aceite</translation>
    </message>
    <message>
        <location filename="../transactiontablemodel.cpp" line="353"/>
        <source>Received with</source>
        <translation>Recebido com</translation>
    </message>
    <message>
        <location filename="../transactiontablemodel.cpp" line="355"/>
        <source>Received from</source>
        <translation>Recebido de</translation>
    </message>
    <message>
        <location filename="../transactiontablemodel.cpp" line="358"/>
        <source>Sent to</source>
        <translation>Enviado para</translation>
    </message>
    <message>
        <location filename="../transactiontablemodel.cpp" line="360"/>
        <source>Payment to yourself</source>
        <translation>Pagamento ao próprio</translation>
    </message>
    <message>
        <location filename="../transactiontablemodel.cpp" line="362"/>
        <source>Mined</source>
        <translation>Minado</translation>
    </message>
    <message>
        <location filename="../transactiontablemodel.cpp" line="400"/>
        <source>(n/a)</source>
        <translation>(n/d)</translation>
    </message>
    <message>
        <location filename="../transactiontablemodel.cpp" line="599"/>
        <source>Transaction status. Hover over this field to show number of confirmations.</source>
        <translation>Estado da transação. Pairar por cima deste campo para mostrar o número de confirmações.</translation>
    </message>
    <message>
        <location filename="../transactiontablemodel.cpp" line="601"/>
        <source>Date and time that the transaction was received.</source>
        <translation>Data e hora a que esta transação foi recebida.</translation>
    </message>
    <message>
        <location filename="../transactiontablemodel.cpp" line="603"/>
        <source>Type of transaction.</source>
        <translation>Tipo de transação.</translation>
    </message>
    <message>
        <location filename="../transactiontablemodel.cpp" line="605"/>
        <source>Destination address of transaction.</source>
        <translation>Endereço de destino da transação.</translation>
    </message>
    <message>
        <location filename="../transactiontablemodel.cpp" line="607"/>
        <source>Amount removed from or added to balance.</source>
        <translation>Quantia retirada ou adicionada ao saldo.</translation>
    </message>
</context>
<context>
    <name>TransactionView</name>
    <message>
        <location filename="../transactionview.cpp" line="55"/>
        <location filename="../transactionview.cpp" line="71"/>
        <source>All</source>
        <translation>Todas</translation>
    </message>
    <message>
        <location filename="../transactionview.cpp" line="56"/>
        <source>Today</source>
        <translation>Hoje</translation>
    </message>
    <message>
        <location filename="../transactionview.cpp" line="57"/>
        <source>This week</source>
        <translation>Esta semana</translation>
    </message>
    <message>
        <location filename="../transactionview.cpp" line="58"/>
        <source>This month</source>
        <translation>Este mês</translation>
    </message>
    <message>
        <location filename="../transactionview.cpp" line="59"/>
        <source>Last month</source>
        <translation>Mês passado</translation>
    </message>
    <message>
        <location filename="../transactionview.cpp" line="60"/>
        <source>This year</source>
        <translation>Este ano</translation>
    </message>
    <message>
        <location filename="../transactionview.cpp" line="61"/>
        <source>Range...</source>
        <translation>Período...</translation>
    </message>
    <message>
        <location filename="../transactionview.cpp" line="72"/>
        <source>Received with</source>
        <translation>Recebida com</translation>
    </message>
    <message>
        <location filename="../transactionview.cpp" line="74"/>
        <source>Sent to</source>
        <translation>Enviada para</translation>
    </message>
    <message>
        <location filename="../transactionview.cpp" line="76"/>
        <source>To yourself</source>
        <translation>Para si</translation>
    </message>
    <message>
        <location filename="../transactionview.cpp" line="77"/>
        <source>Mined</source>
        <translation>Minadas</translation>
    </message>
    <message>
        <location filename="../transactionview.cpp" line="78"/>
        <source>Other</source>
        <translation>Outras</translation>
    </message>
    <message>
        <location filename="../transactionview.cpp" line="85"/>
        <source>Enter address or label to search</source>
        <translation>Escreva endereço ou rótulo a procurar</translation>
    </message>
    <message>
        <location filename="../transactionview.cpp" line="92"/>
        <source>Min amount</source>
        <translation>Quantia mínima</translation>
    </message>
    <message>
        <location filename="../transactionview.cpp" line="126"/>
        <source>Copy address</source>
        <translation>Copiar endereço</translation>
    </message>
    <message>
        <location filename="../transactionview.cpp" line="127"/>
        <source>Copy label</source>
        <translation>Copiar rótulo</translation>
    </message>
    <message>
        <location filename="../transactionview.cpp" line="128"/>
        <source>Copy amount</source>
        <translation>Copiar quantia</translation>
    </message>
    <message>
        <location filename="../transactionview.cpp" line="129"/>
        <source>Edit label</source>
        <translation>Editar rótulo</translation>
    </message>
    <message>
        <location filename="../transactionview.cpp" line="130"/>
        <source>Show transaction details</source>
        <translation type="unfinished"/>
    </message>
    <message>
        <location filename="../transactionview.cpp" line="270"/>
        <source>Export Transaction Data</source>
        <translation>Exportar Dados das Transações</translation>
    </message>
    <message>
        <location filename="../transactionview.cpp" line="271"/>
        <source>Comma separated file (*.csv)</source>
        <translation>Ficheiro separado por vírgula (*.csv)</translation>
    </message>
    <message>
        <location filename="../transactionview.cpp" line="279"/>
        <source>Confirmed</source>
        <translation>Confirmada</translation>
    </message>
    <message>
        <location filename="../transactionview.cpp" line="280"/>
        <source>Date</source>
        <translation>Data</translation>
    </message>
    <message>
        <location filename="../transactionview.cpp" line="281"/>
        <source>Type</source>
        <translation>Tipo</translation>
    </message>
    <message>
        <location filename="../transactionview.cpp" line="282"/>
        <source>Label</source>
        <translation>Rótulo</translation>
    </message>
    <message>
        <location filename="../transactionview.cpp" line="283"/>
        <source>Address</source>
        <translation>Endereço</translation>
    </message>
    <message>
        <location filename="../transactionview.cpp" line="284"/>
        <source>Amount</source>
        <translation>Quantia</translation>
    </message>
    <message>
        <location filename="../transactionview.cpp" line="285"/>
        <source>ID</source>
        <translation>ID</translation>
    </message>
    <message>
        <location filename="../transactionview.cpp" line="289"/>
        <source>Error exporting</source>
        <translation>Erro ao exportar</translation>
    </message>
    <message>
        <location filename="../transactionview.cpp" line="289"/>
        <source>Could not write to file %1.</source>
        <translation>Impossível escrever para o ficheiro %1.</translation>
    </message>
    <message>
        <location filename="../transactionview.cpp" line="384"/>
        <source>Range:</source>
        <translation>Período:</translation>
    </message>
    <message>
        <location filename="../transactionview.cpp" line="392"/>
        <source>to</source>
        <translation>até</translation>
    </message>
</context>
<context>
    <name>VerifyMessageDialog</name>
    <message>
        <location filename="../forms/verifymessagedialog.ui" line="14"/>
        <source>Verify Signed Message</source>
        <translation type="unfinished"/>
    </message>
    <message>
        <location filename="../forms/verifymessagedialog.ui" line="20"/>
        <source>Enter the message and signature below (be careful to correctly copy newlines, spaces, tabs and other invisible characters) to obtain the Bitcoin address used to sign the message.</source>
        <translation type="unfinished"/>
    </message>
    <message>
        <location filename="../forms/verifymessagedialog.ui" line="62"/>
        <source>Verify a message and obtain the Bitcoin address used to sign the message</source>
        <translation type="unfinished"/>
    </message>
    <message>
        <location filename="../forms/verifymessagedialog.ui" line="65"/>
        <source>&amp;Verify Message</source>
        <translation type="unfinished"/>
    </message>
    <message>
        <location filename="../forms/verifymessagedialog.ui" line="79"/>
        <source>Copy the currently selected address to the system clipboard</source>
        <translation>Copie o endereço selecionado para a área de transferência do sistema</translation>
    </message>
    <message>
        <location filename="../forms/verifymessagedialog.ui" line="82"/>
        <source>&amp;Copy Address</source>
        <translation type="unfinished"/>
    </message>
    <message>
        <location filename="../forms/verifymessagedialog.ui" line="93"/>
        <source>Reset all verify message fields</source>
        <translation type="unfinished"/>
    </message>
    <message>
        <location filename="../forms/verifymessagedialog.ui" line="96"/>
        <source>Clear &amp;All</source>
        <translation type="unfinished"/>
    </message>
    <message>
        <location filename="../verifymessagedialog.cpp" line="28"/>
        <source>Enter Bitcoin signature</source>
        <translation type="unfinished"/>
    </message>
    <message>
        <location filename="../verifymessagedialog.cpp" line="29"/>
        <source>Click &quot;Verify Message&quot; to obtain address</source>
        <translation type="unfinished"/>
    </message>
    <message>
        <location filename="../verifymessagedialog.cpp" line="55"/>
        <location filename="../verifymessagedialog.cpp" line="62"/>
        <source>Invalid Signature</source>
        <translation type="unfinished"/>
    </message>
    <message>
        <location filename="../verifymessagedialog.cpp" line="55"/>
        <source>The signature could not be decoded. Please check the signature and try again.</source>
        <translation type="unfinished"/>
    </message>
    <message>
        <location filename="../verifymessagedialog.cpp" line="62"/>
        <source>The signature did not match the message digest. Please check the signature and try again.</source>
        <translation type="unfinished"/>
    </message>
    <message>
        <location filename="../verifymessagedialog.cpp" line="72"/>
        <source>Address not found in address book.</source>
        <translation type="unfinished"/>
    </message>
    <message>
        <location filename="../verifymessagedialog.cpp" line="72"/>
        <source>Address found in address book: %1</source>
        <translation type="unfinished"/>
    </message>
</context>
<context>
    <name>WalletModel</name>
    <message>
        <location filename="../walletmodel.cpp" line="158"/>
        <source>Sending...</source>
        <translation>Enviando...</translation>
    </message>
</context>
<context>
    <name>WindowOptionsPage</name>
    <message>
        <location filename="../optionsdialog.cpp" line="313"/>
        <source>Window</source>
        <translation type="unfinished"/>
    </message>
    <message>
        <location filename="../optionsdialog.cpp" line="316"/>
        <source>&amp;Minimize to the tray instead of the taskbar</source>
        <translation>&amp;Minimizar para a bandeja e não para a barra de ferramentas</translation>
    </message>
    <message>
        <location filename="../optionsdialog.cpp" line="317"/>
        <source>Show only a tray icon after minimizing the window</source>
        <translation>Apenas mostrar o ícone da bandeja depois de minimizar a janela</translation>
    </message>
    <message>
        <location filename="../optionsdialog.cpp" line="320"/>
        <source>M&amp;inimize on close</source>
        <translation type="unfinished"/>
    </message>
    <message>
        <location filename="../optionsdialog.cpp" line="321"/>
        <source>Minimize instead of exit the application when the window is closed. When this option is enabled, the application will be closed only after selecting Quit in the menu.</source>
        <translation>Minimize ao invés de sair da aplicação quando a janela é fechada. Com esta  opção selecionada, a aplicação apenas será encerrada quando escolher Sair da aplicação no menú.</translation>
    </message>
</context>
<context>
    <name>bitcoin-core</name>
    <message>
        <location filename="../bitcoinstrings.cpp" line="43"/>
        <source>Bitcoin version</source>
        <translation>Versão Bitcoin</translation>
    </message>
    <message>
        <location filename="../bitcoinstrings.cpp" line="44"/>
        <source>Usage:</source>
        <translation>Utilização:</translation>
    </message>
    <message>
        <location filename="../bitcoinstrings.cpp" line="45"/>
        <source>Send command to -server or bitcoind</source>
        <translation>Enviar comando para -server ou bitcoind</translation>
    </message>
    <message>
        <location filename="../bitcoinstrings.cpp" line="46"/>
        <source>List commands</source>
        <translation>Listar comandos</translation>
    </message>
    <message>
        <location filename="../bitcoinstrings.cpp" line="47"/>
        <source>Get help for a command</source>
        <translation>Obter ajuda para um comando</translation>
    </message>
    <message>
        <location filename="../bitcoinstrings.cpp" line="49"/>
        <source>Options:</source>
        <translation>Opções:</translation>
    </message>
    <message>
        <location filename="../bitcoinstrings.cpp" line="50"/>
        <source>Specify configuration file (default: bitcoin.conf)</source>
        <translation>Especificar ficheiro de configuração (por defeito: bitcoin.conf)</translation>
    </message>
    <message>
        <location filename="../bitcoinstrings.cpp" line="51"/>
        <source>Specify pid file (default: bitcoind.pid)</source>
        <translation>Especificar ficheiro pid (por defeito: bitcoind.pid)</translation>
    </message>
    <message>
        <location filename="../bitcoinstrings.cpp" line="52"/>
        <source>Generate coins</source>
        <translation>Gerar moedas</translation>
    </message>
    <message>
        <location filename="../bitcoinstrings.cpp" line="53"/>
        <source>Don&apos;t generate coins</source>
        <translation>Não gerar moedas</translation>
    </message>
    <message>
        <location filename="../bitcoinstrings.cpp" line="54"/>
        <source>Specify data directory</source>
        <translation>Especificar pasta de dados</translation>
    </message>
    <message>
        <location filename="../bitcoinstrings.cpp" line="55"/>
        <source>Set database cache size in megabytes (default: 25)</source>
        <translation>Definir o tamanho da cache de base de dados em megabytes (por defeito: 25)</translation>
    </message>
    <message>
        <location filename="../bitcoinstrings.cpp" line="56"/>
        <source>Set database disk log size in megabytes (default: 100)</source>
        <translation>Definir o tamanho de registo do disco de base de dados em megabytes (por defeito: 100)</translation>
    </message>
    <message>
        <location filename="../bitcoinstrings.cpp" line="57"/>
        <source>Specify connection timeout (in milliseconds)</source>
        <translation>Especificar tempo de espera da ligação (em millisegundos)</translation>
    </message>
    <message>
        <location filename="../bitcoinstrings.cpp" line="63"/>
        <source>Listen for connections on &lt;port&gt; (default: 8333 or testnet: 18333)</source>
        <translation>Escute por ligações em &lt;port&gt; (por defeito: 8333 ou testnet: 18333)</translation>
    </message>
    <message>
        <location filename="../bitcoinstrings.cpp" line="64"/>
        <source>Maintain at most &lt;n&gt; connections to peers (default: 125)</source>
        <translation>Manter no máximo &lt;n&gt; ligações a outros nós da rede (por defeito: 125)</translation>
    </message>
    <message>
        <location filename="../bitcoinstrings.cpp" line="66"/>
        <source>Connect only to the specified node</source>
        <translation>Apenas ligar ao nó especificado</translation>
    </message>
    <message>
        <location filename="../bitcoinstrings.cpp" line="67"/>
        <source>Connect to a node to retrieve peer addresses, and disconnect</source>
        <translation type="unfinished"/>
    </message>
    <message>
        <location filename="../bitcoinstrings.cpp" line="68"/>
        <source>Specify your own public address</source>
        <translation type="unfinished"/>
    </message>
    <message>
        <location filename="../bitcoinstrings.cpp" line="69"/>
        <source>Only connect to nodes in network &lt;net&gt; (IPv4 or IPv6)</source>
        <translation type="unfinished"/>
    </message>
    <message>
        <location filename="../bitcoinstrings.cpp" line="70"/>
        <source>Try to discover public IP address (default: 1)</source>
        <translation type="unfinished"/>
    </message>
    <message>
        <location filename="../bitcoinstrings.cpp" line="73"/>
        <source>Bind to given address. Use [host]:port notation for IPv6</source>
        <translation type="unfinished"/>
    </message>
    <message>
        <location filename="../bitcoinstrings.cpp" line="75"/>
        <source>Threshold for disconnecting misbehaving peers (default: 100)</source>
        <translation>Tolerância para desligar nós mal-formados (por defeito: 100)</translation>
    </message>
    <message>
        <location filename="../bitcoinstrings.cpp" line="76"/>
        <source>Number of seconds to keep misbehaving peers from reconnecting (default: 86400)</source>
        <translation>Número de segundos a impedir que nós mal-formados se liguem de novo (por defeito: 86400)</translation>
    </message>
    <message>
        <location filename="../bitcoinstrings.cpp" line="79"/>
        <source>Maximum per-connection receive buffer, &lt;n&gt;*1000 bytes (default: 10000)</source>
        <translation>Armazenamento intermédio de recepção por ligação, &lt;n&gt;*1000 bytes (por defeito: 10000)</translation>
    </message>
    <message>
        <location filename="../bitcoinstrings.cpp" line="80"/>
        <source>Maximum per-connection send buffer, &lt;n&gt;*1000 bytes (default: 10000)</source>
        <translation>Armazenamento intermédio de envio por ligação, &lt;n&gt;*1000 bytes (por defeito: 10000)</translation>
    </message>
    <message>
        <location filename="../bitcoinstrings.cpp" line="83"/>
        <source>Detach block and address databases. Increases shutdown time (default: 0)</source>
        <translation type="unfinished"/>
    </message>
    <message>
        <location filename="../bitcoinstrings.cpp" line="86"/>
        <source>Accept command line and JSON-RPC commands</source>
        <translation>Aceitar comandos da consola e JSON-RPC</translation>
    </message>
    <message>
        <location filename="../bitcoinstrings.cpp" line="87"/>
        <source>Run in the background as a daemon and accept commands</source>
        <translation>Correr o processo como um daemon e aceitar comandos</translation>
    </message>
    <message>
        <location filename="../bitcoinstrings.cpp" line="88"/>
        <source>Use the test network</source>
        <translation>Utilizar a rede de testes - testnet</translation>
    </message>
    <message>
        <location filename="../bitcoinstrings.cpp" line="89"/>
        <source>Output extra debugging information</source>
        <translation>Produzir informação de depuração extraordinária</translation>
    </message>
    <message>
        <location filename="../bitcoinstrings.cpp" line="90"/>
        <source>Prepend debug output with timestamp</source>
        <translation>Preceder informação de depuração com selo temporal</translation>
    </message>
    <message>
        <location filename="../bitcoinstrings.cpp" line="91"/>
        <source>Send trace/debug info to console instead of debug.log file</source>
        <translation>Enviar informação de rastreio/depuração para a consola e não para o ficheiro debug.log</translation>
    </message>
    <message>
        <location filename="../bitcoinstrings.cpp" line="92"/>
        <source>Send trace/debug info to debugger</source>
        <translation>Enviar informação de rastreio/depuração para o depurador</translation>
    </message>
    <message>
        <location filename="../bitcoinstrings.cpp" line="93"/>
        <source>Username for JSON-RPC connections</source>
        <translation>Nome de utilizador para ligações JSON-RPC</translation>
    </message>
    <message>
        <location filename="../bitcoinstrings.cpp" line="94"/>
        <source>Password for JSON-RPC connections</source>
        <translation>Palavra-passe para ligações JSON-RPC</translation>
    </message>
    <message>
        <location filename="../bitcoinstrings.cpp" line="95"/>
        <source>Listen for JSON-RPC connections on &lt;port&gt; (default: 8332)</source>
        <translation>Escutar por ligações JSON-RPC na porta &lt;port&gt; (por defeito: 8332)</translation>
    </message>
    <message>
        <location filename="../bitcoinstrings.cpp" line="96"/>
        <source>Allow JSON-RPC connections from specified IP address</source>
        <translation>Permitir ligações JSON-RPC do endereço IP especificado</translation>
    </message>
    <message>
        <location filename="../bitcoinstrings.cpp" line="97"/>
        <source>Send commands to node running on &lt;ip&gt; (default: 127.0.0.1)</source>
        <translation>Enviar comandos para o nó a correr em &lt;ip&gt; (por defeito: 127.0.0.1)</translation>
    </message>
    <message>
        <location filename="../bitcoinstrings.cpp" line="98"/>
        <source>Execute command when the best block changes (%s in cmd is replaced by block hash)</source>
        <translation>Executar comando quando mudar o melhor bloco (na consola, %s é substituído pela hash do bloco)</translation>
    </message>
    <message>
        <location filename="../bitcoinstrings.cpp" line="101"/>
        <source>Upgrade wallet to latest format</source>
        <translation>Atualize a carteira para o formato mais recente</translation>
    </message>
    <message>
        <location filename="../bitcoinstrings.cpp" line="102"/>
        <source>Set key pool size to &lt;n&gt; (default: 100)</source>
        <translation>Definir o tamanho da memória de chaves para &lt;n&gt; (por defeito: 100)</translation>
    </message>
    <message>
        <location filename="../bitcoinstrings.cpp" line="103"/>
        <source>Rescan the block chain for missing wallet transactions</source>
        <translation>Reexaminar a cadeia de blocos para transações em falta na carteira</translation>
    </message>
    <message>
        <location filename="../bitcoinstrings.cpp" line="104"/>
        <source>How many blocks to check at startup (default: 2500, 0 = all)</source>
        <translation>Verificar quantos blocos ao iniciar (por defeito: 2500, 0 = todos)</translation>
    </message>
    <message>
        <location filename="../bitcoinstrings.cpp" line="105"/>
        <source>How thorough the block verification is (0-6, default: 1)</source>
        <translation>Minuciosidade da verificação de blocos é (0-6, por defeito: 1)</translation>
    </message>
    <message>
        <location filename="../bitcoinstrings.cpp" line="106"/>
        <source>Imports blocks from external blk000?.dat file</source>
        <translation type="unfinished"/>
    </message>
    <message>
        <location filename="../bitcoinstrings.cpp" line="108"/>
        <source>
SSL options: (see the Bitcoin Wiki for SSL setup instructions)</source>
        <translation>
Opções SSL: (ver a Wiki Bitcoin para instruções de configuração SSL)</translation>
    </message>
    <message>
        <location filename="../bitcoinstrings.cpp" line="111"/>
        <source>Use OpenSSL (https) for JSON-RPC connections</source>
        <translation>Usar OpenSSL (https) para ligações JSON-RPC</translation>
    </message>
    <message>
        <location filename="../bitcoinstrings.cpp" line="112"/>
        <source>Server certificate file (default: server.cert)</source>
        <translation>Ficheiro de certificado do servidor (por defeito: server.cert)</translation>
    </message>
    <message>
        <location filename="../bitcoinstrings.cpp" line="113"/>
        <source>Server private key (default: server.pem)</source>
        <translation>Chave privada do servidor (por defeito: server.pem)</translation>
    </message>
    <message>
        <location filename="../bitcoinstrings.cpp" line="114"/>
        <source>Acceptable ciphers (default: TLSv1+HIGH:!SSLv2:!aNULL:!eNULL:!AH:!3DES:@STRENGTH)</source>
        <translation>Cifras aceitáveis (por defeito: TLSv1+HIGH:!SSLv2:!aNULL:!eNULL:!AH:!3DES:@STRENGTH)</translation>
    </message>
    <message>
        <location filename="../bitcoinstrings.cpp" line="145"/>
        <source>Warning: Disk space is low</source>
        <translation type="unfinished"/>
    </message>
    <message>
        <location filename="../bitcoinstrings.cpp" line="107"/>
        <source>This help message</source>
        <translation>Esta mensagem de ajuda</translation>
    </message>
    <message>
        <location filename="../bitcoinstrings.cpp" line="121"/>
        <source>Cannot obtain a lock on data directory %s.  Bitcoin is probably already running.</source>
        <translation>Impossível trancar a pasta de dados %s.  Provavelmente o Bitcoin já está a ser executado.</translation>
    </message>
    <message>
        <location filename="../bitcoinstrings.cpp" line="48"/>
        <source>Bitcoin</source>
        <translation>Bitcoin</translation>
    </message>
    <message>
        <location filename="../bitcoinstrings.cpp" line="30"/>
        <source>Unable to bind to %s on this computer (bind returned error %d, %s)</source>
        <translation type="unfinished"/>
    </message>
    <message>
        <location filename="../bitcoinstrings.cpp" line="58"/>
        <source>Connect through socks proxy</source>
        <translation type="unfinished"/>
    </message>
    <message>
        <location filename="../bitcoinstrings.cpp" line="59"/>
        <source>Select the version of socks proxy to use (4 or 5, 5 is default)</source>
        <translation type="unfinished"/>
    </message>
    <message>
        <location filename="../bitcoinstrings.cpp" line="60"/>
        <source>Do not use proxy for connections to network &lt;net&gt; (IPv4 or IPv6)</source>
        <translation type="unfinished"/>
    </message>
    <message>
        <location filename="../bitcoinstrings.cpp" line="61"/>
        <source>Allow DNS lookups for -addnode, -seednode and -connect</source>
        <translation type="unfinished"/>
    </message>
    <message>
        <location filename="../bitcoinstrings.cpp" line="62"/>
        <source>Pass DNS requests to (SOCKS5) proxy</source>
        <translation type="unfinished"/>
    </message>
    <message>
        <location filename="../bitcoinstrings.cpp" line="142"/>
        <source>Loading addresses...</source>
        <translation>A carregar endereços...</translation>
    </message>
    <message>
        <location filename="../bitcoinstrings.cpp" line="132"/>
        <source>Error loading blkindex.dat</source>
        <translation>Erro ao carregar blkindex.dat</translation>
    </message>
    <message>
        <location filename="../bitcoinstrings.cpp" line="134"/>
        <source>Error loading wallet.dat: Wallet corrupted</source>
        <translation>Erro ao carregar wallet.dat: Carteira danificada</translation>
    </message>
    <message>
        <location filename="../bitcoinstrings.cpp" line="135"/>
        <source>Error loading wallet.dat: Wallet requires newer version of Bitcoin</source>
        <translation>Erro ao carregar wallet.dat: A Carteira requer uma versão mais recente do Bitcoin</translation>
    </message>
    <message>
        <location filename="../bitcoinstrings.cpp" line="136"/>
        <source>Wallet needed to be rewritten: restart Bitcoin to complete</source>
        <translation>A Carteira precisou ser reescrita: reinicie o Bitcoin para completar</translation>
    </message>
    <message>
        <location filename="../bitcoinstrings.cpp" line="137"/>
        <source>Error loading wallet.dat</source>
        <translation>Erro ao carregar wallet.dat</translation>
    </message>
    <message>
        <location filename="../bitcoinstrings.cpp" line="124"/>
        <source>Invalid -proxy address: &apos;%s&apos;</source>
        <translation type="unfinished"/>
    </message>
    <message>
        <location filename="../bitcoinstrings.cpp" line="125"/>
        <source>Unknown network specified in -noproxy: &apos;%s&apos;</source>
        <translation type="unfinished"/>
    </message>
    <message>
        <location filename="../bitcoinstrings.cpp" line="127"/>
        <source>Unknown network specified in -onlynet: &apos;%s&apos;</source>
        <translation type="unfinished"/>
    </message>
    <message>
        <location filename="../bitcoinstrings.cpp" line="126"/>
        <source>Unknown -socks proxy version requested: %i</source>
        <translation type="unfinished"/>
    </message>
    <message>
        <location filename="../bitcoinstrings.cpp" line="128"/>
        <source>Cannot resolve -bind address: &apos;%s&apos;</source>
        <translation type="unfinished"/>
    </message>
    <message>
        <location filename="../bitcoinstrings.cpp" line="129"/>
        <source>Not listening on any port</source>
        <translation type="unfinished"/>
    </message>
    <message>
        <location filename="../bitcoinstrings.cpp" line="130"/>
        <source>Cannot resolve -externalip address: &apos;%s&apos;</source>
        <translation type="unfinished"/>
    </message>
    <message>
        <location filename="../bitcoinstrings.cpp" line="117"/>
        <source>Invalid amount for -paytxfee=&lt;amount&gt;: &apos;%s&apos;</source>
        <translation type="unfinished"/>
    </message>
    <message>
        <location filename="../bitcoinstrings.cpp" line="143"/>
        <source>Error: could not start node</source>
        <translation type="unfinished"/>
    </message>
    <message>
        <location filename="../bitcoinstrings.cpp" line="31"/>
        <source>Error: Wallet locked, unable to create transaction  </source>
        <translation>Erro: Carteira bloqueada, incapaz de criar transação  </translation>
    </message>
    <message>
        <location filename="../bitcoinstrings.cpp" line="32"/>
        <source>Error: This transaction requires a transaction fee of at least %s because of its amount, complexity, or use of recently received funds  </source>
        <translation>Erro: Esta transação requer uma taxa de transação mínima de %s devido á sua quantia, complexidade, ou uso de fundos recebidos recentemente  </translation>
    </message>
    <message>
        <location filename="../bitcoinstrings.cpp" line="35"/>
        <source>Error: Transaction creation failed  </source>
        <translation>Error: Transaction creation failed  </translation>
    </message>
    <message>
        <location filename="../bitcoinstrings.cpp" line="36"/>
        <source>Sending...</source>
        <translation>Enviando...</translation>
    </message>
    <message>
        <location filename="../bitcoinstrings.cpp" line="37"/>
        <source>Error: The transaction was rejected.  This might happen if some of the coins in your wallet were already spent, such as if you used a copy of wallet.dat and coins were spent in the copy but not marked as spent here.</source>
        <translation>Error: The transaction was rejected.  This might happen if some of the coins in your wallet were already spent, such as if you used a copy of wallet.dat and coins were spent in the copy but not marked as spent here.</translation>
    </message>
    <message>
        <location filename="../bitcoinstrings.cpp" line="41"/>
        <source>Invalid amount</source>
        <translation>Quantia inválida</translation>
    </message>
    <message>
        <location filename="../bitcoinstrings.cpp" line="42"/>
        <source>Insufficient funds</source>
        <translation>Insufficient funds</translation>
    </message>
    <message>
        <location filename="../bitcoinstrings.cpp" line="131"/>
        <source>Loading block index...</source>
        <translation>A carregar índice de blocos...</translation>
    </message>
    <message>
        <location filename="../bitcoinstrings.cpp" line="65"/>
        <source>Add a node to connect to and attempt to keep the connection open</source>
        <translation>Adicione um nó ao qual se ligar e tentar manter a ligação aberta</translation>
    </message>
    <message>
        <location filename="../bitcoinstrings.cpp" line="28"/>
        <source>Unable to bind to %s on this computer. Bitcoin is probably already running.</source>
        <translation type="unfinished"/>
    </message>
    <message>
        <location filename="../bitcoinstrings.cpp" line="71"/>
        <source>Find peers using internet relay chat (default: 0)</source>
        <translation>Encontrar pares usando IRC (por defeito: 0)</translation>
    </message>
    <message>
        <location filename="../bitcoinstrings.cpp" line="72"/>
        <source>Accept connections from outside (default: 1)</source>
        <translation>Aceitar ligações externas (por defeito: 1)</translation>
    </message>
    <message>
        <location filename="../bitcoinstrings.cpp" line="74"/>
        <source>Find peers using DNS lookup (default: 1)</source>
        <translation>Encontrar pares usando procura DNS (por defeito: 1)</translation>
    </message>
    <message>
        <location filename="../bitcoinstrings.cpp" line="81"/>
        <source>Use Universal Plug and Play to map the listening port (default: 1)</source>
        <translation>Usar Universal Plug and Play para mapear porta de escuta (por defeito: 1)</translation>
    </message>
    <message>
        <location filename="../bitcoinstrings.cpp" line="82"/>
        <source>Use Universal Plug and Play to map the listening port (default: 0)</source>
        <translation>Usar Universal Plug and Play para mapear porta de escuta (por defeito: 0)</translation>
    </message>
    <message>
        <location filename="../bitcoinstrings.cpp" line="85"/>
        <source>Fee per KB to add to transactions you send</source>
        <translation>Fee per KB to add to transactions you send</translation>
    </message>
    <message>
        <location filename="../bitcoinstrings.cpp" line="118"/>
        <source>Warning: -paytxfee is set very high. This is the transaction fee you will pay if you send a transaction.</source>
        <translation type="unfinished"/>
    </message>
    <message>
        <location filename="../bitcoinstrings.cpp" line="133"/>
        <source>Loading wallet...</source>
        <translation>A carregar carteira...</translation>
    </message>
    <message>
        <location filename="../bitcoinstrings.cpp" line="138"/>
        <source>Cannot downgrade wallet</source>
        <translation>Impossível mudar a carteira para uma versão anterior</translation>
    </message>
    <message>
        <location filename="../bitcoinstrings.cpp" line="139"/>
        <source>Cannot initialize keypool</source>
        <translation>Impossível inicializar keypool</translation>
    </message>
    <message>
        <location filename="../bitcoinstrings.cpp" line="140"/>
        <source>Cannot write default address</source>
        <translation>Impossível escrever endereço por defeito</translation>
    </message>
    <message>
        <location filename="../bitcoinstrings.cpp" line="141"/>
        <source>Rescanning...</source>
        <translation>Reexaminando...</translation>
    </message>
    <message>
        <location filename="../bitcoinstrings.cpp" line="144"/>
        <source>Done loading</source>
        <translation>Carregamento completo</translation>
    </message>
    <message>
        <location filename="../bitcoinstrings.cpp" line="8"/>
        <source>To use the %s option</source>
        <translation>Para usar a opção %s</translation>
    </message>
    <message>
        <location filename="../bitcoinstrings.cpp" line="9"/>
        <source>%s, you must set a rpcpassword in the configuration file:
 %s
It is recommended you use the following random password:
rpcuser=bitcoinrpc
rpcpassword=%s
(you do not need to remember this password)
If the file does not exist, create it with owner-readable-only file permissions.
</source>
        <translation>%s, deverá definir uma palavra-passe de RPC no ficheiro de configuração :
 %s
É recomendado que use a seguinte palavra-passe aleatória:
rpcuser=bitcoinrpc
rpcpassword=%s
(não precisa recordar esta palavra-passe)
Se o ficheiro não existir, crie-o com permissões de leitura apenas para o dono.
</translation>
    </message>
    <message>
        <location filename="../bitcoinstrings.cpp" line="18"/>
        <source>Error</source>
        <translation>Erro</translation>
    </message>
    <message>
        <location filename="../bitcoinstrings.cpp" line="19"/>
        <source>An error occured while setting up the RPC port %i for listening: %s</source>
        <translation>Ocorreu um erro ao definir a porta de escuta %i do serviço RPC: %s</translation>
    </message>
    <message>
        <location filename="../bitcoinstrings.cpp" line="20"/>
        <source>You must set rpcpassword=&lt;password&gt; in the configuration file:
%s
If the file does not exist, create it with owner-readable-only file permissions.</source>
        <translation>Deverá definir rpcpassword=&lt;password&gt; no ficheiro de configuração:
%s
Se o ficheiro não existir, crie-o com permissões de leitura apenas para o dono.</translation>
    </message>
    <message>
        <location filename="../bitcoinstrings.cpp" line="25"/>
        <source>Warning: Please check that your computer&apos;s date and time are correct.  If your clock is wrong Bitcoin will not work properly.</source>
        <translation>Atenção: Por favor verifique que a data e hora do seu computador estão correctas.  Se o seu relógio não estiver certo o Bitcoin não irá funcionar correctamente.</translation>
    </message>
</context>
</TS>