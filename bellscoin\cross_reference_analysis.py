#!/usr/bin/env python3
"""
BELLSCOIN-DOGECOIN CROSS-REFERENCE ANALYSIS
==========================================

Now that we have both Bells<PERSON><PERSON> (2013) and <PERSON><PERSON><PERSON><PERSON> (late 2013/early 2014) codebases,
let's analyze the connections and see if our discovered private key has any correlation
with <PERSON><PERSON><PERSON><PERSON>'s launch.

Discovered Private Key: e5be24df57c43a82d15c2f06bda961296948f8f8eb48501bed1efb929afe0698
Dogecoin Address: DBaox2q5dsyRMTjm4XCpxsRKBY7S6nJtDg
Bitcoin Address: **********************************
"""

import os
import re
import hashlib
from datetime import datetime

# Our discovered treasure
DISCOVERED_PRIVATE_KEY = "e5be24df57c43a82d15c2f06bda961296948f8f8eb48501bed1efb929afe0698"
DOGECOIN_ADDRESS = "DBaox2q5dsyRMTjm4XCpxsRKBY7S6nJtDg"
BITCOIN_ADDRESS = "**********************************"

def search_for_key_in_files(search_key, description=""):
    """Search for the private key or related patterns in all files"""
    print(f"\n🔍 SEARCHING FOR {description}")
    print("=" * 60)
    
    found_files = []
    
    # Search in various formats
    search_patterns = [
        search_key.lower(),
        search_key.upper(),
        search_key,
        f"0x{search_key}",
        f'"{search_key}"',
        f"'{search_key}'",
    ]
    
    for root, dirs, files in os.walk('.'):
        if any(skip in root for skip in ['.git', '__pycache__', 'obj']):
            continue
            
        for file in files:
            if file.endswith(('.cpp', '.h', '.c', '.py', '.md', '.txt', '.conf', '.pro')):
                filepath = os.path.join(root, file)
                try:
                    with open(filepath, 'r', encoding='utf-8', errors='ignore') as f:
                        content = f.read()
                        
                        for pattern in search_patterns:
                            if pattern in content:
                                found_files.append((filepath, pattern))
                                print(f"✅ Found in: {filepath}")
                                print(f"   Pattern: {pattern}")
                                
                                # Show context
                                lines = content.split('\n')
                                for i, line in enumerate(lines):
                                    if pattern in line:
                                        start = max(0, i-2)
                                        end = min(len(lines), i+3)
                                        print(f"   Context (lines {start+1}-{end}):")
                                        for j in range(start, end):
                                            marker = ">>> " if j == i else "    "
                                            print(f"   {marker}{j+1}: {lines[j]}")
                                        break
                                break
                except:
                    continue
    
    if not found_files:
        print(f"❌ No direct references to {description} found")
    
    return found_files

def analyze_dogecoin_genesis():
    """Analyze Dogecoin's genesis block and compare with Bellscoin"""
    print(f"\n🐕 DOGECOIN GENESIS ANALYSIS")
    print("=" * 60)
    
    # Look for Dogecoin genesis information
    dogecoin_files = []
    for root, dirs, files in os.walk('.'):
        for file in files:
            if 'doge' in file.lower() or any(file.endswith(ext) for ext in ['.cpp', '.h']):
                filepath = os.path.join(root, file)
                try:
                    with open(filepath, 'r', encoding='utf-8', errors='ignore') as f:
                        content = f.read()
                        if 'genesis' in content.lower() or 'timestamp' in content.lower():
                            dogecoin_files.append(filepath)
                except:
                    continue
    
    print(f"Found {len(dogecoin_files)} files with potential genesis information:")
    for file in dogecoin_files:
        print(f"  - {file}")
    
    return dogecoin_files

def compare_timestamps():
    """Compare timestamps between Bellscoin and Dogecoin"""
    print(f"\n📅 TIMESTAMP COMPARISON")
    print("=" * 60)
    
    # Bellscoin genesis timestamp
    bellscoin_timestamp = 1383509530  # Nov 3, 2013
    bellscoin_date = datetime.fromtimestamp(bellscoin_timestamp)
    
    print(f"Bellscoin Genesis: {bellscoin_timestamp} ({bellscoin_date})")
    
    # Search for Dogecoin timestamps
    timestamp_patterns = [
        r'nTime\s*=\s*(\d+)',
        r'timestamp\s*=\s*(\d+)',
        r'genesis.*(\d{10})',
        r'(\d{10})',  # Any 10-digit number (Unix timestamp)
    ]
    
    found_timestamps = []
    
    for root, dirs, files in os.walk('.'):
        for file in files:
            if file.endswith(('.cpp', '.h', '.py')):
                filepath = os.path.join(root, file)
                try:
                    with open(filepath, 'r', encoding='utf-8', errors='ignore') as f:
                        content = f.read()
                        
                        for pattern in timestamp_patterns:
                            matches = re.findall(pattern, content, re.IGNORECASE)
                            for match in matches:
                                try:
                                    timestamp = int(match)
                                    # Check if it's a reasonable timestamp (2013-2014 era)
                                    if 1380000000 <= timestamp <= 1420000000:  # Late 2013 to early 2015
                                        date = datetime.fromtimestamp(timestamp)
                                        found_timestamps.append((filepath, timestamp, date))
                                        print(f"Found timestamp: {timestamp} ({date}) in {filepath}")
                                except:
                                    continue
                except:
                    continue
    
    return found_timestamps

def analyze_address_patterns():
    """Look for address patterns that might be related to our discovered addresses"""
    print(f"\n🏠 ADDRESS PATTERN ANALYSIS")
    print("=" * 60)
    
    # Look for our discovered addresses or similar patterns
    addresses_to_search = [
        DOGECOIN_ADDRESS,
        BITCOIN_ADDRESS,
        "DBaox",  # Partial Dogecoin address
        "17SiQ",  # Partial Bitcoin address
    ]
    
    for address in addresses_to_search:
        print(f"\nSearching for: {address}")
        found = search_for_key_in_files(address, f"address pattern '{address}'")
        if not found:
            print(f"  No references found")

def check_for_shared_constants():
    """Look for constants shared between Bellscoin and Dogecoin"""
    print(f"\n🔗 SHARED CONSTANTS ANALYSIS")
    print("=" * 60)
    
    # Known Bellscoin constants
    bellscoin_constants = {
        "486604799": "Script constant",
        "1383509530": "Genesis timestamp", 
        "44481": "Genesis nonce",
        "1e0ffff0": "nBits value",
        "Nintondo": "Genesis message",
    }
    
    for constant, description in bellscoin_constants.items():
        print(f"\nSearching for Bellscoin {description}: {constant}")
        found = search_for_key_in_files(constant, f"Bellscoin {description}")
        if found:
            print(f"  ✅ Found shared constant!")
        else:
            print(f"  ❌ Not found in Dogecoin files")

def main():
    """Main cross-reference analysis"""
    print("🏴‍☠️ BELLSCOIN-DOGECOIN CROSS-REFERENCE ANALYSIS")
    print("Analyzing connections between the discovered private key and Dogecoin...")
    print(f"Private Key: {DISCOVERED_PRIVATE_KEY}")
    print(f"Dogecoin Address: {DOGECOIN_ADDRESS}")
    print(f"Bitcoin Address: {BITCOIN_ADDRESS}")
    
    # 1. Search for the private key directly
    search_for_key_in_files(DISCOVERED_PRIVATE_KEY, "PRIVATE KEY")
    
    # 2. Search for the addresses
    analyze_address_patterns()
    
    # 3. Analyze genesis blocks
    analyze_dogecoin_genesis()
    
    # 4. Compare timestamps
    compare_timestamps()
    
    # 5. Look for shared constants
    check_for_shared_constants()
    
    print(f"\n🎯 CONCLUSION:")
    print("Cross-reference analysis complete. Check the results above for any")
    print("connections between the Bellscoin private key and Dogecoin's codebase.")
    print("This will help confirm if this is indeed the 'key that unlocked Dogecoin's launchpad'!")

if __name__ == "__main__":
    main()
