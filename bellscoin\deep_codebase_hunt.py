#!/usr/bin/env python3
"""
Deep Codebase Treasure Hunt
===========================

This script searches for hidden private keys in the Bellscoin codebase by:
1. Looking for hardcoded hex strings that could be private keys
2. Searching for unusual constants and magic numbers
3. Analyzing build dates and version numbers as potential clues
4. Checking for steganographic patterns in comments and strings
5. Testing combinations of discovered constants

Target Genesis Public Key: 040184710fa689ad5023690c80f3a49c8f13f8d45b8c857fbcbc8bc4a8e4d3eb4b10f4d4604fa08dce601aaf0f470216fe1b51850b4acf21b179c45070ac7b03a9
"""

import os
import re
import hashlib
import binascii
from typing import List, Set, Tuple
from ecdsa import SigningKey, SECP256k1
from ecdsa.util import string_to_number, number_to_string

# Target genesis public key
TARGET_PUBKEY = "040184710fa689ad5023690c80f3a49c8f13f8d45b8c857fbcbc8bc4a8e4d3eb4b10f4d4604fa08dce601aaf0f470216fe1b51850b4acf21b179c45070ac7b03a9"

def sha256_hash(data: str) -> str:
    """Generate SHA256 hash of string"""
    return hashlib.sha256(data.encode()).hexdigest()

def test_private_key(private_key_hex: str) -> bool:
    """Test if a private key generates the target public key"""
    try:
        # Ensure proper length
        if len(private_key_hex) != 64:
            private_key_hex = private_key_hex.zfill(64)
        
        # Convert to integer
        private_key_int = int(private_key_hex, 16)
        if private_key_int == 0 or private_key_int >= SECP256k1.order:
            return False
        
        # Generate public key
        private_key_bytes = number_to_string(private_key_int, SECP256k1.order)
        signing_key = SigningKey.from_string(private_key_bytes, curve=SECP256k1)
        verifying_key = signing_key.get_verifying_key()
        
        # Uncompressed format: 04 + x + y coordinates
        uncompressed_pubkey = "04" + verifying_key.to_string().hex()
        
        return uncompressed_pubkey.lower() == TARGET_PUBKEY.lower()
    except:
        return False

def scan_file_for_hex_strings(filepath: str) -> List[str]:
    """Scan a file for potential hex strings that could be private keys"""
    hex_strings = []
    try:
        with open(filepath, 'r', encoding='utf-8', errors='ignore') as f:
            content = f.read()
            
            # Look for 64-character hex strings (potential private keys)
            hex_pattern = r'\b[0-9a-fA-F]{64}\b'
            matches = re.findall(hex_pattern, content)
            hex_strings.extend(matches)
            
            # Look for 32-character hex strings (half private keys)
            hex_pattern_32 = r'\b[0-9a-fA-F]{32}\b'
            matches_32 = re.findall(hex_pattern_32, content)
            hex_strings.extend(matches_32)
            
            # Look for quoted hex strings
            quoted_hex = r'["\']([0-9a-fA-F]{32,64})["\']'
            quoted_matches = re.findall(quoted_hex, content)
            hex_strings.extend(quoted_matches)
            
    except Exception as e:
        print(f"Error reading {filepath}: {e}")
    
    return hex_strings

def extract_constants_from_file(filepath: str) -> List[str]:
    """Extract numeric constants and magic numbers from source files"""
    constants = []
    try:
        with open(filepath, 'r', encoding='utf-8', errors='ignore') as f:
            content = f.read()
            
            # Look for #define constants
            define_pattern = r'#define\s+\w+\s+([0-9]+)'
            matches = re.findall(define_pattern, content)
            constants.extend(matches)
            
            # Look for static const assignments
            const_pattern = r'static\s+const\s+\w+\s+\w+\s*=\s*([0-9]+)'
            matches = re.findall(const_pattern, content)
            constants.extend(matches)
            
            # Look for large numeric literals
            number_pattern = r'\b([0-9]{6,})\b'
            matches = re.findall(number_pattern, content)
            constants.extend(matches)
            
    except Exception as e:
        print(f"Error reading {filepath}: {e}")
    
    return constants

def scan_codebase() -> Tuple[Set[str], Set[str]]:
    """Scan the entire codebase for potential clues"""
    hex_strings = set()
    constants = set()
    
    # File extensions to scan
    extensions = ['.cpp', '.h', '.c', '.py', '.sh', '.txt', '.md', '.json']
    
    for root, dirs, files in os.walk('.'):
        # Skip certain directories
        if any(skip in root for skip in ['.git', '__pycache__', 'obj']):
            continue
            
        for file in files:
            if any(file.endswith(ext) for ext in extensions):
                filepath = os.path.join(root, file)
                
                # Scan for hex strings
                file_hex = scan_file_for_hex_strings(filepath)
                hex_strings.update(file_hex)
                
                # Extract constants
                file_constants = extract_constants_from_file(filepath)
                constants.update(file_constants)
    
    return hex_strings, constants

def analyze_discovered_data(hex_strings: Set[str], constants: Set[str]):
    """Analyze discovered hex strings and constants"""
    print("🔍 DEEP CODEBASE ANALYSIS")
    print("=" * 50)
    
    # Test direct hex strings
    print(f"\n📊 Found {len(hex_strings)} potential hex strings")
    for i, hex_str in enumerate(sorted(hex_strings), 1):
        if len(hex_str) >= 32:
            print(f"[{i}] Testing hex string: {hex_str[:32]}...")
            if test_private_key(hex_str):
                print(f"🎯 TREASURE FOUND! Hex string: {hex_str}")
                return hex_str
    
    # Test constants as private keys
    print(f"\n📊 Found {len(constants)} numeric constants")
    for i, const in enumerate(sorted(constants), 1):
        if len(const) >= 6:  # Only test longer constants
            print(f"[{i}] Testing constant: {const}")
            
            # Test direct hash of constant
            const_hash = sha256_hash(const)
            if test_private_key(const_hash):
                print(f"🎯 TREASURE FOUND! Constant hash: {const} -> {const_hash}")
                return const_hash
            
            # Test constant as hex if valid
            try:
                if len(const) <= 64 and all(c in '0123456789abcdefABCDEF' for c in const):
                    if test_private_key(const):
                        print(f"🎯 TREASURE FOUND! Direct constant: {const}")
                        return const
            except:
                pass
    
    return None

def main():
    """Main execution function"""
    print("🏴‍☠️ DEEP CODEBASE TREASURE HUNT")
    print("Searching for hidden private keys in the Bellscoin codebase...")
    print(f"Target: {TARGET_PUBKEY[:32]}...")
    
    # Scan the codebase
    print("\n🔍 Scanning codebase for clues...")
    hex_strings, constants = scan_codebase()
    
    # Analyze discovered data
    result = analyze_discovered_data(hex_strings, constants)
    
    if result:
        print(f"\n🏆 TREASURE DISCOVERED: {result}")
    else:
        print("\n💔 No treasure found in direct codebase scan")
        print("🔍 Consider deeper analysis of discovered patterns...")

if __name__ == "__main__":
    main()
