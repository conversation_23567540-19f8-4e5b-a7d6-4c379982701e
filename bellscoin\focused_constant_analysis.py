#!/usr/bin/env python3
"""
Focused Constant Analysis
========================

Analyzing the most promising constants discovered in the codebase:
- 486604799 (from genesis block scriptSig)
- 1383509530 (genesis timestamp)
- 44481 (genesis nonce) 
- 2084524493 (another nonce value)
- Build date: 2013-05-26 22:13:27 -0700

These constants might be combined or transformed to create the private key.
"""

import hashlib
import hmac
import struct
import time
from datetime import datetime
from ecdsa import SigningKey, SECP256k1
from ecdsa.util import string_to_number, number_to_string

# Target genesis public key
TARGET_PUBKEY = "040184710fa689ad5023690c80f3a49c8f13f8d45b8c857fbcbc8bc4a8e4d3eb4b10f4d4604fa08dce601aaf0f470216fe1b51850b4acf21b179c45070ac7b03a9"

# Key constants discovered
GENESIS_TIMESTAMP = 1383509530
GENESIS_NONCE = 44481
SCRIPT_SIG_CONSTANT = 486604799
OTHER_NONCE = 2084524493
BUILD_DATE_STR = "2013-05-26 22:13:27 -0700"

def test_private_key(private_key_hex: str) -> bool:
    """Test if a private key generates the target public key"""
    try:
        if len(private_key_hex) != 64:
            private_key_hex = private_key_hex.zfill(64)
        
        private_key_int = int(private_key_hex, 16)
        if private_key_int == 0 or private_key_int >= SECP256k1.order:
            return False
        
        private_key_bytes = number_to_string(private_key_int, SECP256k1.order)
        signing_key = SigningKey.from_string(private_key_bytes, curve=SECP256k1)
        verifying_key = signing_key.get_verifying_key()
        
        uncompressed_pubkey = "04" + verifying_key.to_string().hex()
        return uncompressed_pubkey.lower() == TARGET_PUBKEY.lower()
    except:
        return False

def sha256_hash(data: str) -> str:
    """Generate SHA256 hash of string"""
    return hashlib.sha256(data.encode()).hexdigest()

def sha256_bytes(data: bytes) -> str:
    """Generate SHA256 hash of bytes"""
    return hashlib.sha256(data).hexdigest()

def test_constant_combinations():
    """Test various combinations of the discovered constants"""
    print("🔍 TESTING CONSTANT COMBINATIONS")
    print("=" * 50)
    
    constants = [
        GENESIS_TIMESTAMP,
        GENESIS_NONCE, 
        SCRIPT_SIG_CONSTANT,
        OTHER_NONCE
    ]
    
    # Test 1: Direct hashing of individual constants
    print("\n1. Testing direct hashes of constants:")
    for const in constants:
        const_str = str(const)
        hash_result = sha256_hash(const_str)
        print(f"   SHA256('{const_str}') = {hash_result[:32]}...")
        if test_private_key(hash_result):
            print(f"🎯 TREASURE FOUND! Constant: {const_str}")
            return hash_result
    
    # Test 2: Concatenated constants
    print("\n2. Testing concatenated constants:")
    for i, const1 in enumerate(constants):
        for j, const2 in enumerate(constants):
            if i != j:
                combined = str(const1) + str(const2)
                hash_result = sha256_hash(combined)
                print(f"   SHA256('{const1}{const2}') = {hash_result[:32]}...")
                if test_private_key(hash_result):
                    print(f"🎯 TREASURE FOUND! Combined: {combined}")
                    return hash_result
    
    # Test 3: Mathematical operations
    print("\n3. Testing mathematical operations:")
    operations = [
        (GENESIS_TIMESTAMP + GENESIS_NONCE, f"{GENESIS_TIMESTAMP} + {GENESIS_NONCE}"),
        (GENESIS_TIMESTAMP - GENESIS_NONCE, f"{GENESIS_TIMESTAMP} - {GENESIS_NONCE}"),
        (GENESIS_TIMESTAMP * GENESIS_NONCE, f"{GENESIS_TIMESTAMP} * {GENESIS_NONCE}"),
        (SCRIPT_SIG_CONSTANT + GENESIS_NONCE, f"{SCRIPT_SIG_CONSTANT} + {GENESIS_NONCE}"),
        (SCRIPT_SIG_CONSTANT * GENESIS_NONCE, f"{SCRIPT_SIG_CONSTANT} * {GENESIS_NONCE}"),
    ]
    
    for result, description in operations:
        result_str = str(result)
        hash_result = sha256_hash(result_str)
        print(f"   SHA256({description}) = {hash_result[:32]}...")
        if test_private_key(hash_result):
            print(f"🎯 TREASURE FOUND! Operation: {description} = {result}")
            return hash_result
    
    # Test 4: Binary representations
    print("\n4. Testing binary packed representations:")
    for const in constants:
        # Pack as different integer sizes
        for fmt in ['<I', '>I', '<Q', '>Q']:  # little/big endian 32/64 bit
            try:
                packed = struct.pack(fmt, const)
                hash_result = sha256_bytes(packed)
                print(f"   SHA256(pack('{fmt}', {const})) = {hash_result[:32]}...")
                if test_private_key(hash_result):
                    print(f"🎯 TREASURE FOUND! Packed: {fmt} {const}")
                    return hash_result
            except:
                pass
    
    # Test 5: HMAC with constants as keys
    print("\n5. Testing HMAC combinations:")
    message = "Nintondo"
    for const in constants:
        key = str(const).encode()
        hmac_result = hmac.new(key, message.encode(), hashlib.sha256).hexdigest()
        print(f"   HMAC-SHA256(key={const}, msg='{message}') = {hmac_result[:32]}...")
        if test_private_key(hmac_result):
            print(f"🎯 TREASURE FOUND! HMAC key: {const}")
            return hmac_result
    
    return None

def test_build_date_analysis():
    """Analyze the build date for clues"""
    print("\n🔍 TESTING BUILD DATE ANALYSIS")
    print("=" * 50)
    
    # Parse build date: "2013-05-26 22:13:27 -0700"
    build_date = "2013-05-26 22:13:27 -0700"
    
    # Extract components
    date_parts = [
        "20130526",
        "221327", 
        "2013",
        "05",
        "26",
        "22",
        "13", 
        "27"
    ]
    
    print(f"Build date: {build_date}")
    
    for part in date_parts:
        hash_result = sha256_hash(part)
        print(f"   SHA256('{part}') = {hash_result[:32]}...")
        if test_private_key(hash_result):
            print(f"🎯 TREASURE FOUND! Date part: {part}")
            return hash_result
    
    # Test combinations with Nintondo
    for part in date_parts:
        combined = "Nintondo" + part
        hash_result = sha256_hash(combined)
        print(f"   SHA256('Nintondo{part}') = {hash_result[:32]}...")
        if test_private_key(hash_result):
            print(f"🎯 TREASURE FOUND! Nintondo + date: {combined}")
            return hash_result
    
    return None

def test_script_sig_analysis():
    """Deep analysis of the scriptSig constant 486604799"""
    print("\n🔍 TESTING SCRIPT SIG CONSTANT ANALYSIS")
    print("=" * 50)
    
    const = 486604799
    print(f"ScriptSig constant: {const}")
    print(f"Hex: 0x{const:08x}")
    print(f"Binary: {bin(const)}")
    
    # Test various transformations
    transformations = [
        (str(const), "direct string"),
        (hex(const)[2:], "hex without 0x"),
        (f"{const:08x}", "8-digit hex"),
        (bin(const)[2:], "binary without 0b"),
        (str(const)[::-1], "reversed string"),
        (f"{const:08x}"[::-1], "reversed hex"),
    ]
    
    for value, description in transformations:
        hash_result = sha256_hash(value)
        print(f"   SHA256({description}: '{value}') = {hash_result[:32]}...")
        if test_private_key(hash_result):
            print(f"🎯 TREASURE FOUND! ScriptSig {description}: {value}")
            return hash_result
        
        # Also test with Nintondo prefix
        combined = "Nintondo" + value
        hash_result = sha256_hash(combined)
        print(f"   SHA256('Nintondo{value}') = {hash_result[:32]}...")
        if test_private_key(hash_result):
            print(f"🎯 TREASURE FOUND! Nintondo + {description}: {combined}")
            return hash_result
    
    return None

def main():
    """Main execution function"""
    print("🏴‍☠️ FOCUSED CONSTANT ANALYSIS")
    print("Analyzing the most promising constants from the codebase...")
    print(f"Target: {TARGET_PUBKEY[:32]}...")
    
    # Test different analysis approaches
    result = test_constant_combinations()
    if result:
        print(f"\n🏆 TREASURE DISCOVERED: {result}")
        return
    
    result = test_build_date_analysis()
    if result:
        print(f"\n🏆 TREASURE DISCOVERED: {result}")
        return
    
    result = test_script_sig_analysis()
    if result:
        print(f"\n🏆 TREASURE DISCOVERED: {result}")
        return
    
    print("\n💔 No treasure found in focused constant analysis")
    print("🔍 The private key might require a different approach...")

if __name__ == "__main__":
    main()
