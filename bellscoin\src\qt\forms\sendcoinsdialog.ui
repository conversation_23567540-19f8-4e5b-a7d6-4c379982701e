<?xml version="1.0" encoding="UTF-8"?>
<ui version="4.0">
 <class>SendCoinsDialog</class>
 <widget class="QDialog" name="SendCoinsDialog">
  <property name="geometry">
   <rect>
    <x>0</x>
    <y>0</y>
    <width>686</width>
    <height>217</height>
   </rect>
  </property>
  <property name="windowTitle">
   <string>Send Coins</string>
  </property>
  <layout class="QVBoxLayout" name="verticalLayout">
   <item>
    <widget class="QScrollArea" name="scrollArea">
     <property name="widgetResizable">
      <bool>true</bool>
     </property>
     <widget class="QWidget" name="scrollAreaWidgetContents">
      <property name="geometry">
       <rect>
        <x>0</x>
        <y>0</y>
        <width>666</width>
        <height>165</height>
       </rect>
      </property>
      <layout class="QVBoxLayout" name="verticalLayout_2">
       <property name="margin">
        <number>0</number>
       </property>
       <item>
        <layout class="QVBoxLayout" name="entries">
         <property name="spacing">
          <number>6</number>
         </property>
        </layout>
       </item>
       <item>
        <spacer name="verticalSpacer">
         <property name="orientation">
          <enum>Qt::Vertical</enum>
         </property>
         <property name="sizeHint" stdset="0">
          <size>
           <width>20</width>
           <height>40</height>
          </size>
         </property>
        </spacer>
       </item>
      </layout>
     </widget>
    </widget>
   </item>
   <item>
    <layout class="QHBoxLayout" name="horizontalLayout">
     <item>
      <widget class="QPushButton" name="addButton">
       <property name="toolTip">
        <string>Send to multiple recipients at once</string>
       </property>
       <property name="text">
        <string>&amp;Add Recipient</string>
       </property>
       <property name="icon">
        <iconset resource="../bitcoin.qrc">
         <normaloff>:/icons/add</normaloff>:/icons/add</iconset>
       </property>
       <property name="autoDefault">
        <bool>false</bool>
       </property>
      </widget>
     </item>
     <item>
      <widget class="QPushButton" name="clearButton">
       <property name="sizePolicy">
        <sizepolicy hsizetype="Minimum" vsizetype="Fixed">
         <horstretch>0</horstretch>
         <verstretch>0</verstretch>
        </sizepolicy>
       </property>
       <property name="toolTip">
        <string>Remove all transaction fields</string>
       </property>
       <property name="text">
        <string>Clear &amp;All</string>
       </property>
       <property name="icon">
        <iconset resource="../bitcoin.qrc">
         <normaloff>:/icons/remove</normaloff>:/icons/remove</iconset>
       </property>
       <property name="autoRepeatDelay">
        <number>300</number>
       </property>
       <property name="autoDefault">
        <bool>false</bool>
       </property>
      </widget>
     </item>
     <item>
      <layout class="QHBoxLayout" name="horizontalLayout_2">
       <property name="spacing">
        <number>3</number>
       </property>
       <item>
        <widget class="QLabel" name="label">
         <property name="text">
          <string>Balance:</string>
         </property>
        </widget>
       </item>
       <item>
        <widget class="QLabel" name="labelBalance">
         <property name="cursor">
          <cursorShape>IBeamCursor</cursorShape>
         </property>
         <property name="text">
          <string>123.456 BEL</string>
         </property>
         <property name="textInteractionFlags">
          <set>Qt::LinksAccessibleByMouse|Qt::TextSelectableByKeyboard|Qt::TextSelectableByMouse</set>
         </property>
        </widget>
       </item>
      </layout>
     </item>
     <item>
      <spacer name="horizontalSpacer">
       <property name="orientation">
        <enum>Qt::Horizontal</enum>
       </property>
       <property name="sizeHint" stdset="0">
        <size>
         <width>40</width>
         <height>20</height>
        </size>
       </property>
      </spacer>
     </item>
     <item>
      <widget class="QPushButton" name="sendButton">
       <property name="minimumSize">
        <size>
         <width>150</width>
         <height>0</height>
        </size>
       </property>
       <property name="toolTip">
        <string>Confirm the send action</string>
       </property>
       <property name="text">
        <string>&amp;Send</string>
       </property>
       <property name="icon">
        <iconset resource="../bitcoin.qrc">
         <normaloff>:/icons/send</normaloff>:/icons/send</iconset>
       </property>
       <property name="default">
        <bool>true</bool>
       </property>
      </widget>
     </item>
    </layout>
   </item>
  </layout>
 </widget>
 <resources>
  <include location="../bitcoin.qrc"/>
 </resources>
 <connections/>
</ui>
