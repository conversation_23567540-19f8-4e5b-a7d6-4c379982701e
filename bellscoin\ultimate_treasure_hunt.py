#!/usr/bin/env python3
"""
ULTIMATE BELLSCOIN TREASURE HUNT
===============================

This script conducts the most comprehensive search for the hidden private key
in Bellscoin's 2013 codebase that allegedly unlocked <PERSON><PERSON><PERSON><PERSON>'s launchpad.

Based on the treasure hunt image clues and deep codebase analysis:
- Target: "Nintondo" misspelling is intentional
- Connection to Dogecoin launch (December 2013)  
- Hidden in genesis block constants and patterns
- Still works today

Target Genesis Public Key: 040184710fa689ad5023690c80f3a49c8f13f8d45b8c857fbcbc8bc4a8e4d3eb4b10f4d4604fa08dce601aaf0f470216fe1b51850b4acf21b179c45070ac7b03a9
"""

import hashlib
import hmac
import struct
import time
import itertools
from datetime import datetime
from ecdsa import SigningKey, SECP256k1
from ecdsa.util import string_to_number, number_to_string

# Target genesis public key (uncompressed format)
TARGET_PUBKEY = "040184710fa689ad5023690c80f3a49c8f13f8d45b8c857fbcbc8bc4a8e4d3eb4b10f4d4604fa08dce601aaf0f470216fe1b51850b4acf21b179c45070ac7b03a9"

# Genesis block constants from main.cpp
GENESIS_TIMESTAMP = 1383509530  # Nov 3, 2013
GENESIS_NONCE = 44481
SCRIPT_CONSTANT = 486604799  # 0x1d00ffff
NBITS = 0x1e0ffff0

# Dogecoin launch date (December 6, 2013)
DOGECOIN_TIMESTAMP = int(datetime(2013, 12, 6).timestamp())

def sha256_hash(data: str) -> str:
    """Calculate SHA256 hash of input string"""
    return hashlib.sha256(data.encode('utf-8')).hexdigest()

def test_private_key(private_key_hex: str) -> bool:
    """Test if a private key generates the target public key"""
    try:
        # Ensure proper length
        if len(private_key_hex) != 64:
            private_key_hex = private_key_hex.zfill(64)
        
        # Convert to integer
        private_key_int = int(private_key_hex, 16)
        if private_key_int == 0 or private_key_int >= SECP256k1.order:
            return False
        
        # Generate public key
        private_key_bytes = number_to_string(private_key_int, SECP256k1.order)
        signing_key = SigningKey.from_string(private_key_bytes, curve=SECP256k1)
        verifying_key = signing_key.get_verifying_key()
        
        # Uncompressed format: 04 + x + y coordinates
        uncompressed_pubkey = "04" + verifying_key.to_string().hex()
        
        return uncompressed_pubkey.lower() == TARGET_PUBKEY.lower()
    except:
        return False

def test_candidate(name: str, private_key_hex: str) -> bool:
    """Test a private key candidate and report results"""
    print(f"Testing: {name}")
    print(f"Key: {private_key_hex[:32]}...")
    
    if test_private_key(private_key_hex):
        print(f"🎯 TREASURE FOUND! {name}")
        print(f"🎯 Private Key: {private_key_hex}")
        print(f"🎯 This is the key that unlocked Dogecoin's launchpad!")
        return True
    else:
        print("❌ No match")
        return False

def hunt_phase_1_basic_combinations():
    """Phase 1: Test basic Nintondo combinations"""
    print("\n🏴‍☠️ PHASE 1: BASIC NINTONDO COMBINATIONS")
    print("=" * 60)
    
    candidates = [
        # Direct hashes
        ("SHA256('Nintondo')", sha256_hash("Nintondo")),
        ("SHA256('Nintendo')", sha256_hash("Nintendo")),
        
        # With genesis timestamp
        ("SHA256('Nintondo1383509530')", sha256_hash("Nintondo1383509530")),
        ("SHA256('Nintondo44481')", sha256_hash("Nintondo44481")),
        ("SHA256('138350953044481')", sha256_hash("138350953044481")),
        
        # With Dogecoin connection
        ("SHA256('NintondoDogecoin')", sha256_hash("NintondoDogecoin")),
        ("SHA256('DogecoinNintondo')", sha256_hash("DogecoinNintondo")),
        ("SHA256('Nintondo20131206')", sha256_hash("Nintondo20131206")),
        
        # With script constant
        ("SHA256('Nintondo486604799')", sha256_hash("Nintondo486604799")),
        ("SHA256('486604799Nintondo')", sha256_hash("486604799Nintondo")),
    ]
    
    for name, key in candidates:
        if test_candidate(name, key):
            return key
    
    return None

def hunt_phase_2_advanced_patterns():
    """Phase 2: Test advanced patterns and combinations"""
    print("\n🏴‍☠️ PHASE 2: ADVANCED PATTERNS")
    print("=" * 60)
    
    # Test signature header patterns (0x1D, 0x1E from key.cpp)
    sig_headers = [0x1B, 0x1C, 0x1D, 0x1E]
    
    candidates = []
    
    # Combine Nintondo with signature headers
    for header in sig_headers:
        candidates.extend([
            (f"SHA256('Nintondo{header:02x}')", sha256_hash(f"Nintondo{header:02x}")),
            (f"SHA256('Nintondo{header}')", sha256_hash(f"Nintondo{header}")),
        ])
    
    # Test hex representations of constants
    hex_constants = [
        ("Script constant as hex", f"{SCRIPT_CONSTANT:08x}"),
        ("nBits as hex", f"{NBITS:08x}"),
        ("Combined hex", f"{SCRIPT_CONSTANT:08x}{NBITS:08x}"),
    ]
    
    for name, hex_val in hex_constants:
        if len(hex_val) == 64:  # Valid private key length
            candidates.append((name, hex_val))
        else:
            candidates.append((f"SHA256({name})", sha256_hash(hex_val)))
    
    # Test all candidates
    for name, key in candidates:
        if test_candidate(name, key):
            return key
    
    return None

def hunt_phase_3_steganographic():
    """Phase 3: Look for steganographic patterns in the public key itself"""
    print("\n🏴‍☠️ PHASE 3: STEGANOGRAPHIC ANALYSIS")
    print("=" * 60)
    
    pubkey = TARGET_PUBKEY[2:]  # Remove '04' prefix
    
    # Look for patterns that might encode "Nintondo"
    nintondo_hex = "Nintondo".encode().hex()
    print(f"Nintondo in hex: {nintondo_hex}")
    
    # Check if Nintondo hex appears in pubkey
    if nintondo_hex in pubkey.lower():
        print(f"🎯 Found 'Nintondo' hex pattern in public key!")
        position = pubkey.lower().find(nintondo_hex)
        print(f"Position: {position}")
    
    # Look for repeating patterns
    for length in range(4, 16, 2):  # Even lengths for hex pairs
        for start in range(0, len(pubkey) - length, 2):
            pattern = pubkey[start:start + length]
            count = pubkey.count(pattern)
            if count > 1:
                print(f"Repeating pattern: {pattern} (appears {count} times)")
                pattern_hash = sha256_hash(pattern)
                if test_candidate(f"Pattern hash: {pattern}", pattern_hash):
                    return pattern_hash
    
    return None

def main():
    """Main treasure hunting execution"""
    print("🏴‍☠️ ULTIMATE BELLSCOIN TREASURE HUNT")
    print("Searching for the private key that unlocked Dogecoin's launchpad...")
    print(f"Target: {TARGET_PUBKEY[:32]}...")
    print(f"Genesis timestamp: {GENESIS_TIMESTAMP} ({datetime.fromtimestamp(GENESIS_TIMESTAMP)})")
    print(f"Genesis nonce: {GENESIS_NONCE}")
    print(f"Script constant: {SCRIPT_CONSTANT} (0x{SCRIPT_CONSTANT:08x})")
    
    start_time = time.time()
    
    # Phase 1: Basic combinations
    result = hunt_phase_1_basic_combinations()
    if result:
        print(f"\n🏆 TREASURE DISCOVERED IN PHASE 1: {result}")
        return result
    
    # Phase 2: Advanced patterns
    result = hunt_phase_2_advanced_patterns()
    if result:
        print(f"\n🏆 TREASURE DISCOVERED IN PHASE 2: {result}")
        return result
    
    # Phase 3: Steganographic analysis
    result = hunt_phase_3_steganographic()
    if result:
        print(f"\n🏆 TREASURE DISCOVERED IN PHASE 3: {result}")
        return result
    
    elapsed = time.time() - start_time
    print(f"\n💔 No treasure found after {elapsed:.2f} seconds")
    print("🔍 The private key might be hidden in a more complex pattern...")
    print("🔍 Consider analyzing the hex comment string or other codebase patterns...")
    
    return None

if __name__ == "__main__":
    main()
