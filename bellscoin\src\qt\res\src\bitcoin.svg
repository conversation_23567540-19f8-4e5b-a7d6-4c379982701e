<?xml version="1.0" encoding="UTF-8" standalone="no"?>
<!-- Created with Inkscape (http://www.inkscape.org/) -->

<svg
   xmlns:dc="http://purl.org/dc/elements/1.1/"
   xmlns:cc="http://creativecommons.org/ns#"
   xmlns:rdf="http://www.w3.org/1999/02/22-rdf-syntax-ns#"
   xmlns:svg="http://www.w3.org/2000/svg"
   xmlns="http://www.w3.org/2000/svg"
   xmlns:xlink="http://www.w3.org/1999/xlink"
   xmlns:sodipodi="http://sodipodi.sourceforge.net/DTD/sodipodi-0.dtd"
   xmlns:inkscape="http://www.inkscape.org/namespaces/inkscape"
   id="svg2"
   version="1.1"
   inkscape:version="0.48.0 r9654"
   width="256"
   height="256"
   sodipodi:docname="bitcoin.svg"
   inkscape:export-filename="/store/orion/projects/bitcoin/BC_Logo_icon32.png"
   inkscape:export-xdpi="11.25"
   inkscape:export-ydpi="11.25"
   style="display:inline">
  <metadata
     id="metadata8">
    <rdf:RDF>
      <cc:Work
         rdf:about="">
        <dc:format>image/svg+xml</dc:format>
        <dc:type
           rdf:resource="http://purl.org/dc/dcmitype/StillImage" />
        <dc:title></dc:title>
      </cc:Work>
    </rdf:RDF>
  </metadata>
  <defs
     id="defs6">
    <linearGradient
       id="linearGradient3800">
      <stop
         style="stop-color:#fbba6c;stop-opacity:1;"
         offset="0"
         id="stop3802" />
      <stop
         style="stop-color:#f89825;stop-opacity:1;"
         offset="1"
         id="stop3804" />
    </linearGradient>
    <filter
       inkscape:collect="always"
       id="filter3788">
      <feGaussianBlur
         inkscape:collect="always"
         stdDeviation="7.7328864"
         id="feGaussianBlur3790" />
    </filter>
    <radialGradient
       inkscape:collect="always"
       xlink:href="#linearGradient3800"
       id="radialGradient3806"
       cx="137.01819"
       cy="136.81316"
       fx="137.01819"
       fy="136.81316"
       r="120.22619"
       gradientTransform="matrix(1,0,0,0.99768745,0,0.29784356)"
       gradientUnits="userSpaceOnUse" />
  </defs>
  <sodipodi:namedview
     pagecolor="#ffffff"
     bordercolor="#666666"
     borderopacity="1"
     objecttolerance="10"
     gridtolerance="10"
     guidetolerance="10"
     inkscape:pageopacity="0"
     inkscape:pageshadow="2"
     inkscape:window-width="1204"
     inkscape:window-height="972"
     id="namedview4"
     showgrid="false"
     inkscape:zoom="1"
     inkscape:cx="-54.927212"
     inkscape:cy="128"
     inkscape:window-x="238"
     inkscape:window-y="143"
     inkscape:window-maximized="0"
     inkscape:current-layer="layer1" />
  <g
     inkscape:groupmode="layer"
     id="layer2"
     inkscape:label="Shadow"
     style="display:inline"
     sodipodi:insensitive="true">
    <path
       sodipodi:nodetypes="sssssssssssscssssscsssscscssssssssssscscsssssssscscssssscssscssssssscsssssssss"
       inkscape:connector-curvature="0"
       id="path2986"
       d="M 111.77934,251.6677 C 67.147549,243.82973 30.788461,211.94464 17.111244,168.64858 7.1011053,136.9609 11.409199,100.0445 28.433065,71.630835 45.23974,43.579693 71.151762,24.409692 103.78036,15.888094 c 12.1652,-3.177178 33.00458,-3.99623 46.6577,-1.833789 45.59908,7.22219 82.56109,39.080427 96.48709,83.164011 10.01013,31.687684 5.70204,68.604084 -11.32183,97.017744 -16.8025,28.04418 -42.64131,47.16473 -75.34729,55.75648 -7.52834,1.97768 -11.60054,2.41566 -24.95273,2.68377 -10.90952,0.21906 -18.34974,-0.0999 -23.52396,-1.00861 z m 20.17619,-31.35425 c 0.63075,-2.39846 2.14458,-8.28561 3.36407,-13.08254 1.56481,-6.15521 2.69488,-8.60563 3.84031,-8.32722 0.89267,0.21696 7.39067,0.68131 14.43998,1.03189 16.31195,0.81124 23.9944,-1.14509 31.43693,-8.00537 7.63877,-7.04117 11.30337,-15.63267 11.82635,-27.72636 0.37592,-8.69327 0.10152,-10.44523 -2.27687,-14.53617 -1.48738,-2.55836 -5.17935,-6.48313 -8.20438,-8.7217 l -5.50006,-4.07013 4.37131,-1.87838 c 9.83682,-4.22697 14.47489,-11.84516 15.1401,-24.86816 0.41587,-8.14174 0.12573,-9.95645 -2.32406,-14.536178 -3.41249,-6.379435 -11.7129,-12.875301 -21.81801,-17.074696 -4.15735,-1.727672 -7.66196,-3.187464 -7.78803,-3.243983 -0.12607,-0.05651 1.28534,-6.088871 3.13645,-13.405229 1.85112,-7.316357 3.26253,-13.372021 3.13645,-13.457031 -0.12607,-0.08501 -2.84573,-0.778731 -6.04368,-1.541604 -3.19796,-0.762872 -6.6666,-1.640092 -7.70811,-1.949379 -1.54868,-0.459905 -2.47321,1.773902 -5.07541,12.262964 -2.215,8.928331 -3.69157,12.825303 -4.85951,12.825303 -2.30435,0 -12.01076,-2.489633 -12.01076,-3.080685 0,-0.263336 1.37739,-5.975631 3.06085,-12.693987 1.68345,-6.718356 2.78086,-12.49516 2.43868,-12.837341 -1.06646,-1.066455 -13.66473,-4.07084 -14.61292,-3.484827 -0.49474,0.305758 -2.13899,5.806491 -3.65393,12.223852 -1.51493,6.417361 -3.0693,12.177423 -3.45416,12.800138 -0.42537,0.68826 -6.84309,-0.416984 -16.36715,-2.818708 -8.617084,-2.173005 -15.989649,-3.583696 -16.383486,-3.13487 -0.393848,0.448826 -1.334199,3.432558 -2.089673,6.630515 -0.755474,3.197957 -1.623352,6.656241 -1.928612,7.685075 -0.438573,1.478093 0.970946,2.194444 6.717302,3.413902 7.407149,1.571906 12.549199,5.114005 12.549199,8.644526 0,3.559986 -18.770806,76.736553 -20.092504,78.329103 -1.007682,1.21419 -2.807318,1.40957 -7.196833,0.78135 -11.551776,-1.65329 -10.897277,-1.95681 -14.648875,6.7937 -1.878049,4.38054 -3.41465,8.32832 -3.41465,8.77286 0,0.44453 6.14881,2.29784 13.664008,4.11848 7.515199,1.82065 14.792647,3.72851 16.172114,4.23971 l 2.508106,0.92945 -3.31877,13.11716 c -1.825323,7.21442 -3.215633,13.21726 -3.089563,13.33965 0.549874,0.53382 15.058297,4.01313 15.382497,3.68892 0.19792,-0.19791 1.74688,-5.83164 3.44215,-12.51942 1.69526,-6.68778 3.30839,-12.52544 3.58472,-12.97256 0.443,-0.71676 11.73522,1.68597 12.76544,2.7162 0.21981,0.21981 -1.08467,6.26629 -2.89884,13.43664 l -3.29849,13.03696 4.8136,1.23571 c 2.6475,0.67965 5.33693,1.42786 5.97652,1.66271 3.63321,1.33406 5.19855,0.40079 6.2902,-3.75024 z m 0.68716,-43.88623 c -0.63959,-0.21166 -5.21848,-1.29512 -10.17531,-2.40768 -4.95684,-1.11255 -9.01243,-2.23295 -9.01243,-2.48975 0,-0.65492 8.11353,-33.1852 8.69895,-34.87745 0.34957,-1.01046 3.32218,-0.62569 10.96233,1.41895 12.84914,3.43866 18.00073,5.54314 23.11146,9.44128 8.35514,6.37277 10.49752,14.36232 5.75489,21.46161 -3.94917,5.91153 -9.15813,8.03413 -19.45529,7.92788 -4.79693,-0.0496 -9.245,-0.26317 -9.8846,-0.47484 z m 2.50991,-54.7082 c -3.73627,-0.93479 -7.21128,-1.958 -7.72221,-2.27376 -0.72057,-0.44534 6.3168,-31.740173 7.3756,-32.798964 0.64276,-0.642776 19.09068,4.708848 22.8588,6.631196 2.34875,1.19824 5.92162,4.059214 7.93975,6.357724 2.94919,3.358944 3.66929,5.201504 3.66929,9.388734 0,4.31035 -0.68714,5.95944 -3.98073,9.55341 -3.93586,4.2948 -4.08991,4.34657 -13.664,4.59252 -5.49512,0.14117 -12.62142,-0.48635 -16.4765,-1.45086 z"
       style="opacity:0.8176923;fill:#000000;fill-opacity:1;filter:url(#filter3788)" />
  </g>
  <g
     inkscape:groupmode="layer"
     id="layer1"
     inkscape:label="Front"
     style="display:inline">
    <path
       style="fill:url(#radialGradient3806);fill-opacity:1;display:inline"
       d="M 107.77934,247.6677 C 63.147549,239.82973 26.788461,207.94464 13.111244,164.64858 3.1011053,132.9609 7.4091993,96.044496 24.433065,67.630835 41.23974,39.579693 67.151762,20.409692 99.780356,11.888094 111.94556,8.7109163 132.78494,7.8918643 146.43806,10.054305 c 45.59908,7.22219 82.56109,39.080427 96.48709,83.164011 10.01013,31.687684 5.70204,68.604084 -11.32183,97.017744 -16.8025,28.04418 -42.64131,47.16473 -75.34729,55.75648 -7.52834,1.97768 -11.60054,2.41566 -24.95273,2.68377 -10.90952,0.21906 -18.34974,-0.0999 -23.52396,-1.00861 z m 20.17619,-31.35425 c 0.63075,-2.39846 2.14458,-8.28561 3.36407,-13.08254 1.56481,-6.15521 2.69488,-8.60563 3.84031,-8.32722 0.89267,0.21696 7.39067,0.68131 14.43998,1.03189 16.31195,0.81124 23.9944,-1.14509 31.43693,-8.00537 7.63877,-7.04117 11.30337,-15.63267 11.82635,-27.72636 0.37592,-8.69327 0.10152,-10.44523 -2.27687,-14.53617 -1.48738,-2.55836 -5.17935,-6.48313 -8.20438,-8.7217 l -5.50006,-4.07013 4.37131,-1.87838 c 9.83682,-4.22697 14.47489,-11.84516 15.1401,-24.86816 0.41587,-8.141744 0.12573,-9.956454 -2.32406,-14.536178 -3.41249,-6.379435 -11.7129,-12.875301 -21.81801,-17.074696 -4.15735,-1.727672 -7.66196,-3.187464 -7.78803,-3.243983 -0.12607,-0.05651 1.28534,-6.088871 3.13645,-13.405229 1.85112,-7.316357 3.26253,-13.372021 3.13645,-13.457031 -0.12607,-0.08501 -2.84573,-0.778731 -6.04368,-1.541604 -3.19796,-0.762872 -6.6666,-1.640092 -7.70811,-1.949379 -1.54868,-0.459905 -2.47321,1.773902 -5.07541,12.262964 -2.215,8.928331 -3.69157,12.825303 -4.85951,12.825303 -2.30435,0 -12.01076,-2.489633 -12.01076,-3.080685 0,-0.263336 1.37739,-5.975631 3.06085,-12.693987 1.68345,-6.718356 2.78086,-12.49516 2.43868,-12.837341 -1.06646,-1.066455 -13.66473,-4.07084 -14.61292,-3.484827 -0.49474,0.305758 -2.13899,5.806491 -3.65393,12.223852 -1.51493,6.417361 -3.0693,12.177423 -3.45416,12.800138 -0.42537,0.68826 -6.84309,-0.416984 -16.36715,-2.818708 -8.617084,-2.173005 -15.989649,-3.583696 -16.383486,-3.13487 -0.393848,0.448826 -1.334199,3.432558 -2.089673,6.630515 -0.755474,3.197957 -1.623352,6.656241 -1.928612,7.685075 -0.438573,1.478093 0.970946,2.194444 6.717302,3.413902 7.407145,1.571906 12.549199,5.114005 12.549199,8.644526 0,3.559986 -18.770806,76.736553 -20.092504,78.329103 -1.007682,1.21419 -2.807318,1.40957 -7.196833,0.78135 -11.551776,-1.65329 -10.897277,-1.95681 -14.648875,6.7937 -1.878049,4.38054 -3.41465,8.32832 -3.41465,8.77286 0,0.44453 6.14881,2.29784 13.664008,4.11848 7.515199,1.82065 14.792647,3.72851 16.172114,4.23971 l 2.508106,0.92945 -3.31877,13.11716 c -1.825323,7.21442 -3.215633,13.21726 -3.089563,13.33965 0.549874,0.53382 15.058293,4.01313 15.382493,3.68892 0.19792,-0.19791 1.74688,-5.83164 3.442154,-12.51942 1.69526,-6.68778 3.30839,-12.52544 3.58472,-12.97256 0.443,-0.71676 11.73522,1.68597 12.76544,2.7162 0.21981,0.21981 -1.08467,6.26629 -2.89884,13.43664 l -3.29849,13.03696 4.8136,1.23571 c 2.6475,0.67965 5.33693,1.42786 5.97652,1.66271 3.63321,1.33406 5.19855,0.40079 6.2902,-3.75024 z m 0.68716,-43.88623 c -0.63959,-0.21166 -5.21848,-1.29512 -10.17531,-2.40768 -4.95684,-1.11255 -9.01243,-2.23295 -9.01243,-2.48975 0,-0.65492 8.11353,-33.1852 8.69895,-34.87745 0.34957,-1.01046 3.32218,-0.62569 10.96233,1.41895 12.84914,3.43866 18.00073,5.54314 23.11146,9.44128 8.35514,6.37277 10.49752,14.36232 5.75489,21.46161 -3.94917,5.91153 -9.15813,8.03413 -19.45529,7.92788 -4.79693,-0.0496 -9.245,-0.26317 -9.8846,-0.47484 z m 2.50991,-54.7082 c -3.73627,-0.93479 -7.21128,-1.958 -7.72221,-2.27376 -0.72057,-0.44534 6.3168,-31.740173 7.3756,-32.798964 0.64276,-0.642776 19.09068,4.708848 22.8588,6.631196 2.34875,1.19824 5.92162,4.059214 7.93975,6.357724 2.94919,3.35894 3.66929,5.201504 3.66929,9.388734 0,4.31035 -0.68714,5.95944 -3.98073,9.55341 -3.93586,4.2948 -4.08991,4.34657 -13.664,4.59252 -5.49512,0.14117 -12.62142,-0.48635 -16.4765,-1.45086 z"
       id="path2987"
       inkscape:connector-curvature="0"
       sodipodi:nodetypes="sssssssssssscssssscsssscscssssssssssscscsssssssscscssssscssscssssssscsssssssss"
       inkscape:export-xdpi="11.98"
       inkscape:export-ydpi="11.98" />
  </g>
</svg>
