import hashlib
import base58

def pubkey_to_address(pubkey_hex, addr_prefix=25):
    """Convert a public key to a Bellscoin address."""
    # Step 1: Decode the hex public key
    pubkey_bytes = bytes.fromhex(pubkey_hex)
    
    # Step 2: Apply SHA-256 to the public key
    sha256_hash = hashlib.sha256(pubkey_bytes).digest()
    
    # Step 3: Apply RIPEMD-160 to the result
    ripemd160_hash = hashlib.new('ripemd160')
    ripemd160_hash.update(sha256_hash)
    hash160 = ripemd160_hash.digest()
    
    # Step 4: Add version byte (25 for Bellscoin)
    versioned_hash = bytes([addr_prefix]) + hash160
    
    # Step 5: Calculate checksum (first 4 bytes of double SHA-256)
    checksum = hashlib.sha256(hashlib.sha256(versioned_hash).digest()).digest()[:4]
    
    # Step 6: Append checksum to versioned hash
    binary_address = versioned_hash + checksum
    
    # Step 7: Convert to Base58
    address = base58.b58encode(binary_address).decode('utf-8')
    
    return address

# The public key from the Bellscoin genesis block
genesis_pubkey = "040184710fa689ad5023690c80f3a49c8f13f8d45b8c857fbcbc8bc4a8e4d3eb4b10f4d4604fa08dce601aaf0f470216fe1b51850b4acf21b179c45070ac7b03a9"

# Convert to Bellscoin address (prefix 25)
bells_address = pubkey_to_address(genesis_pubkey, 25)
print(f"Bellscoin Address: {bells_address}")

# For comparison, convert to Bitcoin address (prefix 0)
bitcoin_address = pubkey_to_address(genesis_pubkey, 0)
print(f"Bitcoin Address: {bitcoin_address}")

# For comparison, convert to Dogecoin address (prefix 30)
dogecoin_address = pubkey_to_address(genesis_pubkey, 30)
print(f"Dogecoin Address: {dogecoin_address}")