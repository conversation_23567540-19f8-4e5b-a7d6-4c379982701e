#!/usr/bin/env python3
"""
Verify Address Generation
========================

Let's verify the addresses generated from our discovered private key
and see if we can match what you're seeing in the wallets.
"""

import hashlib
from ecdsa import Signing<PERSON>ey, SECP256k1
from ecdsa.util import string_to_number, number_to_string

PRIVATE_KEY = "e5be24df57c43a82d15c2f06bda961296948f8f8eb48501bed1efb929afe0698"

def base58_encode(data):
    """Base58 encoding"""
    alphabet = "**********************************************************"
    num = int.from_bytes(data, 'big')

    if num == 0:
        return alphabet[0]

    result = ""
    while num > 0:
        num, remainder = divmod(num, 58)
        result = alphabet[remainder] + result

    # Add leading zeros
    for byte in data:
        if byte == 0:
            result = alphabet[0] + result
        else:
            break

    return result

def get_public_keys(private_key_hex):
    """Get both compressed and uncompressed public keys"""
    private_key_int = int(private_key_hex, 16)
    private_key_bytes = number_to_string(private_key_int, SECP256k1.order)
    signing_key = SigningKey.from_string(private_key_bytes, curve=SECP256k1)
    verifying_key = signing_key.get_verifying_key()

    # Uncompressed: 04 + x + y
    uncompressed = "04" + verifying_key.to_string().hex()

    # Compressed: 02/03 + x (depending on y parity)
    x_bytes = verifying_key.to_string()[:32]
    y_bytes = verifying_key.to_string()[32:]
    y_int = int.from_bytes(y_bytes, 'big')

    if y_int % 2 == 0:
        compressed = "02" + x_bytes.hex()
    else:
        compressed = "03" + x_bytes.hex()

    return uncompressed, compressed

def generate_address(public_key_hex, version_byte):
    """Generate address from public key"""
    # Remove prefix and get bytes
    if public_key_hex.startswith('04'):
        pubkey_bytes = bytes.fromhex(public_key_hex[2:])
    elif public_key_hex.startswith('02') or public_key_hex.startswith('03'):
        pubkey_bytes = bytes.fromhex(public_key_hex)
    else:
        pubkey_bytes = bytes.fromhex(public_key_hex)

    # SHA256 then RIPEMD160
    sha256_hash = hashlib.sha256(pubkey_bytes).digest()
    ripemd160_hash = hashlib.new('ripemd160', sha256_hash).digest()

    # Add version byte
    versioned_hash = bytes([version_byte]) + ripemd160_hash

    # Double SHA256 for checksum
    checksum = hashlib.sha256(hashlib.sha256(versioned_hash).digest()).digest()[:4]

    # Combine and encode
    address_bytes = versioned_hash + checksum
    return base58_encode(address_bytes)

def main():
    print("🔍 ADDRESS VERIFICATION")
    print("=" * 50)
    print(f"Private Key: {PRIVATE_KEY}")
    print()

    # Get public keys
    uncompressed_pubkey, compressed_pubkey = get_public_keys(PRIVATE_KEY)

    print("📋 PUBLIC KEYS:")
    print(f"Uncompressed: {uncompressed_pubkey}")
    print(f"Compressed:   {compressed_pubkey}")
    print()

    # Generate addresses for different cryptocurrencies
    print("🏠 GENERATED ADDRESSES:")

    # Bitcoin addresses (version byte 0x00)
    btc_uncompressed = generate_address(uncompressed_pubkey, 0x00)
    btc_compressed = generate_address(compressed_pubkey, 0x00)

    print(f"Bitcoin (uncompressed): {btc_uncompressed}")
    print(f"Bitcoin (compressed):   {btc_compressed}")

    # Dogecoin addresses (version byte 0x1e = 30)
    doge_uncompressed = generate_address(uncompressed_pubkey, 0x1e)
    doge_compressed = generate_address(compressed_pubkey, 0x1e)

    print(f"Dogecoin (uncompressed): {doge_uncompressed}")
    print(f"Dogecoin (compressed):   {doge_compressed}")

    # Litecoin addresses (version byte 0x30 = 48)
    ltc_uncompressed = generate_address(uncompressed_pubkey, 0x30)
    ltc_compressed = generate_address(compressed_pubkey, 0x30)

    print(f"Litecoin (uncompressed): {ltc_uncompressed}")
    print(f"Litecoin (compressed):   {ltc_compressed}")

    # Bellscoin addresses - need to determine version byte
    # Bellscoin is based on Litecoin, so likely uses similar version bytes
    # Let's try a few possibilities:

    # Try version byte 0x19 = 25 (common for altcoins starting with 'B')
    bells_uncompressed_25 = generate_address(uncompressed_pubkey, 0x19)
    bells_compressed_25 = generate_address(compressed_pubkey, 0x19)

    # Try version byte 0x55 = 85 (another common 'B' prefix)
    bells_uncompressed_85 = generate_address(uncompressed_pubkey, 0x55)
    bells_compressed_85 = generate_address(compressed_pubkey, 0x55)

    print(f"Bellscoin v25 (uncompressed): {bells_uncompressed_25}")
    print(f"Bellscoin v25 (compressed):   {bells_compressed_25}")
    print(f"Bellscoin v85 (uncompressed): {bells_uncompressed_85}")
    print(f"Bellscoin v85 (compressed):   {bells_compressed_85}")

    print()
    print("🎯 COMPARISON WITH YOUR RESULTS:")
    print(f"Your Dogecoin address:  DBaox2q5dsyRMTjm4XCpxsRKBY7S6nJtDg")
    print(f"Your Bitcoin address:   **********************************")
    print()

    # Check matches
    addresses_generated = [btc_uncompressed, btc_compressed, doge_uncompressed, doge_compressed, ltc_uncompressed, ltc_compressed]

    if "DBaox2q5dsyRMTjm4XCpxsRKBY7S6nJtDg" in addresses_generated:
        print("✅ Dogecoin address MATCHES!")
    else:
        print("❌ Dogecoin address does NOT match")

    if "**********************************" in addresses_generated:
        print("✅ Bitcoin address MATCHES!")
    else:
        print("❌ Bitcoin address does NOT match")

    print()
    print("🤔 ANALYSIS:")
    if "DBaox2q5dsyRMTjm4XCpxsRKBY7S6nJtDg" not in addresses_generated:
        print("The addresses don't match, which suggests:")
        print("1. Different derivation method used by wallets")
        print("2. Different private key format interpretation")
        print("3. We might need to look for other private keys in the codebase")
        print("4. The wallet might be using a different encoding")

if __name__ == "__main__":
    main()
