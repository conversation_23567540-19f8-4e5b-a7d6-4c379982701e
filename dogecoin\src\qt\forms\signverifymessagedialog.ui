<?xml version="1.0" encoding="UTF-8"?>
<ui version="4.0">
 <class>SignVerifyMessageDialog</class>
 <widget class="QDialog" name="SignVerifyMessageDialog">
  <property name="geometry">
   <rect>
    <x>0</x>
    <y>0</y>
    <width>700</width>
    <height>380</height>
   </rect>
  </property>
  <property name="windowTitle">
   <string>Signatures - Sign / Verify a Message</string>
  </property>
  <property name="modal">
   <bool>true</bool>
  </property>
  <layout class="QVBoxLayout" name="verticalLayout">
   <item>
    <widget class="QTabWidget" name="tabWidget">
     <property name="currentIndex">
      <number>0</number>
     </property>
     <widget class="QWidget" name="tabSignMessage">
      <attribute name="title">
       <string>&amp;Sign Message</string>
      </attribute>
      <layout class="QVBoxLayout" name="verticalLayout_SM">
       <item>
        <widget class="QLabel" name="infoLabel_SM">
         <property name="text">
          <string>You can sign messages with your addresses to prove you own them. Be careful not to sign anything vague, as phishing attacks may try to trick you into signing your identity over to them. Only sign fully-detailed statements you agree to.</string>
         </property>
         <property name="textFormat">
          <enum>Qt::PlainText</enum>
         </property>
         <property name="wordWrap">
          <bool>true</bool>
         </property>
        </widget>
       </item>
       <item>
        <layout class="QHBoxLayout" name="horizontalLayout_1_SM">
         <property name="spacing">
          <number>0</number>
         </property>
         <item>
          <widget class="QValidatedLineEdit" name="addressIn_SM">
           <property name="toolTip">
            <string>The address to sign the message with (e.g. 1NS17iag9jJgTHD1VXjvLCEnZuQ3rJDE9L)</string>
           </property>
           <property name="maxLength">
            <number>34</number>
           </property>
          </widget>
         </item>
         <item>
          <widget class="QPushButton" name="addressBookButton_SM">
           <property name="toolTip">
            <string>Choose an address from the address book</string>
           </property>
           <property name="text">
            <string/>
           </property>
           <property name="icon">
            <iconset resource="../bitcoin.qrc">
             <normaloff>:/icons/address-book</normaloff>:/icons/address-book</iconset>
           </property>
           <property name="shortcut">
            <string>Alt+A</string>
           </property>
           <property name="autoDefault">
            <bool>false</bool>
           </property>
          </widget>
         </item>
         <item>
          <widget class="QPushButton" name="pasteButton_SM">
           <property name="toolTip">
            <string>Paste address from clipboard</string>
           </property>
           <property name="text">
            <string/>
           </property>
           <property name="icon">
            <iconset resource="../bitcoin.qrc">
             <normaloff>:/icons/editpaste</normaloff>:/icons/editpaste</iconset>
           </property>
           <property name="shortcut">
            <string>Alt+P</string>
           </property>
           <property name="autoDefault">
            <bool>false</bool>
           </property>
          </widget>
         </item>
        </layout>
       </item>
       <item>
        <widget class="QPlainTextEdit" name="messageIn_SM">
         <property name="toolTip">
          <string>Enter the message you want to sign here</string>
         </property>
        </widget>
       </item>
       <item>
        <layout class="QHBoxLayout" name="horizontalLayout_2_SM">
         <property name="spacing">
          <number>0</number>
         </property>
         <item>
          <widget class="QLineEdit" name="signatureOut_SM">
           <property name="font">
            <font>
             <italic>true</italic>
            </font>
           </property>
           <property name="readOnly">
            <bool>true</bool>
           </property>
          </widget>
         </item>
         <item>
          <widget class="QPushButton" name="copySignatureButton_SM">
           <property name="toolTip">
            <string>Copy the current signature to the system clipboard</string>
           </property>
           <property name="text">
            <string/>
           </property>
           <property name="icon">
            <iconset resource="../bitcoin.qrc">
             <normaloff>:/icons/editcopy</normaloff>:/icons/editcopy</iconset>
           </property>
           <property name="autoDefault">
            <bool>false</bool>
           </property>
          </widget>
         </item>
        </layout>
       </item>
       <item>
        <layout class="QHBoxLayout" name="horizontalLayout_3_SM">
         <item>
          <widget class="QPushButton" name="signMessageButton_SM">
           <property name="toolTip">
            <string>Sign the message to prove you own this Bitcoin address</string>
           </property>
           <property name="text">
            <string>&amp;Sign Message</string>
           </property>
           <property name="icon">
            <iconset resource="../bitcoin.qrc">
             <normaloff>:/icons/edit</normaloff>:/icons/edit</iconset>
           </property>
           <property name="autoDefault">
            <bool>false</bool>
           </property>
          </widget>
         </item>
         <item>
          <widget class="QPushButton" name="clearButton_SM">
           <property name="toolTip">
            <string>Reset all sign message fields</string>
           </property>
           <property name="text">
            <string>Clear &amp;All</string>
           </property>
           <property name="icon">
            <iconset resource="../bitcoin.qrc">
             <normaloff>:/icons/remove</normaloff>:/icons/remove</iconset>
           </property>
           <property name="autoDefault">
            <bool>false</bool>
           </property>
          </widget>
         </item>
         <item>
          <spacer name="horizontalSpacer_1_SM">
           <property name="orientation">
            <enum>Qt::Horizontal</enum>
           </property>
           <property name="sizeHint" stdset="0">
            <size>
             <width>40</width>
             <height>48</height>
            </size>
           </property>
          </spacer>
         </item>
         <item>
          <widget class="QLabel" name="statusLabel_SM">
           <property name="font">
            <font>
             <weight>75</weight>
             <bold>true</bold>
            </font>
           </property>
           <property name="text">
            <string/>
           </property>
           <property name="wordWrap">
            <bool>true</bool>
           </property>
          </widget>
         </item>
         <item>
          <spacer name="horizontalSpacer_2_SM">
           <property name="orientation">
            <enum>Qt::Horizontal</enum>
           </property>
           <property name="sizeHint" stdset="0">
            <size>
             <width>40</width>
             <height>48</height>
            </size>
           </property>
          </spacer>
         </item>
        </layout>
       </item>
      </layout>
     </widget>
     <widget class="QWidget" name="tabVerifyMessage">
      <attribute name="title">
       <string>&amp;Verify Message</string>
      </attribute>
      <layout class="QVBoxLayout" name="verticalLayout_VM">
       <item>
        <widget class="QLabel" name="infoLabel_VM">
         <property name="text">
          <string>Enter the signing address, message (ensure you copy line breaks, spaces, tabs, etc. exactly) and signature below to verify the message. Be careful not to read more into the signature than what is in the signed message itself, to avoid being tricked by a man-in-the-middle attack.</string>
         </property>
         <property name="textFormat">
          <enum>Qt::PlainText</enum>
         </property>
         <property name="alignment">
          <set>Qt::AlignLeading|Qt::AlignLeft|Qt::AlignTop</set>
         </property>
         <property name="wordWrap">
          <bool>true</bool>
         </property>
        </widget>
       </item>
       <item>
        <layout class="QHBoxLayout" name="horizontalLayout_1_VM">
         <property name="spacing">
          <number>0</number>
         </property>
         <item>
          <widget class="QValidatedLineEdit" name="addressIn_VM">
           <property name="toolTip">
            <string>The address the message was signed with (e.g. 1NS17iag9jJgTHD1VXjvLCEnZuQ3rJDE9L)</string>
           </property>
           <property name="maxLength">
            <number>34</number>
           </property>
          </widget>
         </item>
         <item>
          <widget class="QPushButton" name="addressBookButton_VM">
           <property name="toolTip">
            <string>Choose an address from the address book</string>
           </property>
           <property name="text">
            <string/>
           </property>
           <property name="icon">
            <iconset resource="../bitcoin.qrc">
             <normaloff>:/icons/address-book</normaloff>:/icons/address-book</iconset>
           </property>
           <property name="shortcut">
            <string>Alt+A</string>
           </property>
           <property name="autoDefault">
            <bool>false</bool>
           </property>
          </widget>
         </item>
        </layout>
       </item>
       <item>
        <widget class="QPlainTextEdit" name="messageIn_VM"/>
       </item>
       <item>
        <widget class="QValidatedLineEdit" name="signatureIn_VM"/>
       </item>
       <item>
        <layout class="QHBoxLayout" name="horizontalLayout_2_VM">
         <item>
          <widget class="QPushButton" name="verifyMessageButton_VM">
           <property name="toolTip">
            <string>Verify the message to ensure it was signed with the specified Bitcoin address</string>
           </property>
           <property name="text">
            <string>&amp;Verify Message</string>
           </property>
           <property name="icon">
            <iconset resource="../bitcoin.qrc">
             <normaloff>:/icons/transaction_0</normaloff>:/icons/transaction_0</iconset>
           </property>
           <property name="autoDefault">
            <bool>false</bool>
           </property>
          </widget>
         </item>
         <item>
          <widget class="QPushButton" name="clearButton_VM">
           <property name="toolTip">
            <string>Reset all verify message fields</string>
           </property>
           <property name="text">
            <string>Clear &amp;All</string>
           </property>
           <property name="icon">
            <iconset resource="../bitcoin.qrc">
             <normaloff>:/icons/remove</normaloff>:/icons/remove</iconset>
           </property>
           <property name="autoDefault">
            <bool>false</bool>
           </property>
          </widget>
         </item>
         <item>
          <spacer name="horizontalSpacer_1_VM">
           <property name="orientation">
            <enum>Qt::Horizontal</enum>
           </property>
           <property name="sizeHint" stdset="0">
            <size>
             <width>40</width>
             <height>48</height>
            </size>
           </property>
          </spacer>
         </item>
         <item>
          <widget class="QLabel" name="statusLabel_VM">
           <property name="font">
            <font>
             <weight>75</weight>
             <bold>true</bold>
            </font>
           </property>
           <property name="text">
            <string/>
           </property>
           <property name="wordWrap">
            <bool>true</bool>
           </property>
          </widget>
         </item>
         <item>
          <spacer name="horizontalSpacer_2_VM">
           <property name="orientation">
            <enum>Qt::Horizontal</enum>
           </property>
           <property name="sizeHint" stdset="0">
            <size>
             <width>40</width>
             <height>48</height>
            </size>
           </property>
          </spacer>
         </item>
        </layout>
       </item>
      </layout>
     </widget>
    </widget>
   </item>
  </layout>
 </widget>
 <customwidgets>
  <customwidget>
   <class>QValidatedLineEdit</class>
   <extends>QLineEdit</extends>
   <header>qvalidatedlineedit.h</header>
  </customwidget>
 </customwidgets>
 <resources>
  <include location="../bitcoin.qrc"/>
 </resources>
 <connections/>
</ui>
