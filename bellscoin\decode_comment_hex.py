#!/usr/bin/env python3
"""
Decode Comment Hex Analysis
===========================

Analyzing the long hex string found in the commented genesis block section:
04ffff001d01044cd14d61792032322c20323031332c2031323a313620612e6d2e204544543a204a6170616e9273204e696b6b65692053746f636b2041766572616765204a503a4e494b202b312e3737252c20776869636820656e6465642061742074686569722068696768657374206c6576656c20696e206d6f7265207468616e206669766520796561727320696e2065616368206f6620746865206c6173742074687265652074726164696e672073657373696f6e732c20636c696d6265642061206675727468657220312e3225205765646e6573646179

This might contain the private key or clues to derive it.
"""

import hashlib
import binascii
from ecdsa import SigningKey, SECP256k1
from ecdsa.util import string_to_number, number_to_string

# Target genesis public key
TARGET_PUBKEY = "040184710fa689ad5023690c80f3a49c8f13f8d45b8c857fbcbc8bc4a8e4d3eb4b10f4d4604fa08dce601aaf0f470216fe1b51850b4acf21b179c45070ac7b03a9"

# The hex string from the comment
COMMENT_HEX = "04ffff001d01044cd14d61792032322c20323031332c2031323a313620612e6d2e204544543a204a6170616e9273204e696b6b65692053746f636b2041766572616765204a503a4e494b202b312e3737252c20776869636820656e6465642061742074686569722068696768657374206c6576656c20696e206d6f7265207468616e206669766520796561727320696e2065616368206f6620746865206c6173742074687265652074726164696e672073657373696f6e732c20636c696d6265642061206675727468657220312e3225205765646e6573646179"

def test_private_key(private_key_hex: str) -> bool:
    """Test if a private key generates the target public key"""
    try:
        if len(private_key_hex) != 64:
            private_key_hex = private_key_hex.zfill(64)
        
        private_key_int = int(private_key_hex, 16)
        if private_key_int == 0 or private_key_int >= SECP256k1.order:
            return False
        
        private_key_bytes = number_to_string(private_key_int, SECP256k1.order)
        signing_key = SigningKey.from_string(private_key_bytes, curve=SECP256k1)
        verifying_key = signing_key.get_verifying_key()
        
        uncompressed_pubkey = "04" + verifying_key.to_string().hex()
        return uncompressed_pubkey.lower() == TARGET_PUBKEY.lower()
    except:
        return False

def sha256_hash(data: str) -> str:
    """Generate SHA256 hash of string"""
    return hashlib.sha256(data.encode()).hexdigest()

def sha256_bytes(data: bytes) -> str:
    """Generate SHA256 hash of bytes"""
    return hashlib.sha256(data).hexdigest()

def analyze_comment_hex():
    """Analyze the hex string from the comment"""
    print("🔍 ANALYZING COMMENT HEX STRING")
    print("=" * 50)
    
    print(f"Hex string length: {len(COMMENT_HEX)} characters")
    print(f"Hex string: {COMMENT_HEX[:64]}...")
    
    # Decode the hex to bytes
    try:
        decoded_bytes = binascii.unhexlify(COMMENT_HEX)
        print(f"Decoded bytes length: {len(decoded_bytes)}")
        
        # Try to decode as ASCII/UTF-8
        try:
            decoded_text = decoded_bytes.decode('utf-8')
            print(f"Decoded text: {decoded_text}")
        except:
            print("Cannot decode as UTF-8")
            # Show first few bytes as ASCII if possible
            ascii_part = ""
            for byte in decoded_bytes[:50]:
                if 32 <= byte <= 126:
                    ascii_part += chr(byte)
                else:
                    ascii_part += f"\\x{byte:02x}"
            print(f"Partial ASCII: {ascii_part}")
        
    except Exception as e:
        print(f"Error decoding hex: {e}")
        return None
    
    # Test the hex string itself as a private key (if right length)
    print(f"\n🔑 Testing hex string as private key...")
    if len(COMMENT_HEX) == 64:
        if test_private_key(COMMENT_HEX):
            print(f"🎯 TREASURE FOUND! Direct hex: {COMMENT_HEX}")
            return COMMENT_HEX
    
    # Test hash of the hex string
    hex_hash = sha256_hash(COMMENT_HEX)
    print(f"SHA256 of hex string: {hex_hash}")
    if test_private_key(hex_hash):
        print(f"🎯 TREASURE FOUND! Hash of hex: {hex_hash}")
        return hex_hash
    
    # Test hash of the decoded bytes
    if 'decoded_bytes' in locals():
        bytes_hash = sha256_bytes(decoded_bytes)
        print(f"SHA256 of decoded bytes: {bytes_hash}")
        if test_private_key(bytes_hash):
            print(f"🎯 TREASURE FOUND! Hash of bytes: {bytes_hash}")
            return bytes_hash
    
    # Test hash of decoded text (if available)
    if 'decoded_text' in locals():
        text_hash = sha256_hash(decoded_text)
        print(f"SHA256 of decoded text: {text_hash}")
        if test_private_key(text_hash):
            print(f"🎯 TREASURE FOUND! Hash of text: {text_hash}")
            return text_hash
        
        # Test combinations with Nintondo
        nintondo_combo = "Nintondo" + decoded_text
        combo_hash = sha256_hash(nintondo_combo)
        print(f"SHA256 of 'Nintondo' + text: {combo_hash[:32]}...")
        if test_private_key(combo_hash):
            print(f"🎯 TREASURE FOUND! Nintondo combo: {combo_hash}")
            return combo_hash
    
    return None

def analyze_hex_chunks():
    """Analyze chunks of the hex string"""
    print(f"\n🔍 ANALYZING HEX CHUNKS")
    print("=" * 50)
    
    # Split into 64-character chunks (potential private keys)
    chunks = [COMMENT_HEX[i:i+64] for i in range(0, len(COMMENT_HEX), 64)]
    
    print(f"Found {len(chunks)} 64-character chunks:")
    for i, chunk in enumerate(chunks):
        print(f"Chunk {i+1}: {chunk}")
        if len(chunk) == 64:
            if test_private_key(chunk):
                print(f"🎯 TREASURE FOUND! Chunk {i+1}: {chunk}")
                return chunk
        
        # Test hash of chunk
        chunk_hash = sha256_hash(chunk)
        if test_private_key(chunk_hash):
            print(f"🎯 TREASURE FOUND! Hash of chunk {i+1}: {chunk_hash}")
            return chunk_hash
    
    # Try 32-character chunks (half private keys)
    chunks_32 = [COMMENT_HEX[i:i+32] for i in range(0, len(COMMENT_HEX), 32)]
    
    print(f"\nFound {len(chunks_32)} 32-character chunks:")
    for i, chunk in enumerate(chunks_32):
        if len(chunk) == 32:
            # Pad to 64 characters
            padded = chunk.ljust(64, '0')
            if test_private_key(padded):
                print(f"🎯 TREASURE FOUND! Padded chunk {i+1}: {padded}")
                return padded
            
            # Test hash
            chunk_hash = sha256_hash(chunk)
            if test_private_key(chunk_hash):
                print(f"🎯 TREASURE FOUND! Hash of 32-chunk {i+1}: {chunk_hash}")
                return chunk_hash
    
    return None

def main():
    """Main execution function"""
    print("🏴‍☠️ COMMENT HEX DECODER")
    print("Analyzing the hex string found in the commented genesis block...")
    print(f"Target: {TARGET_PUBKEY[:32]}...")
    
    # Analyze the comment hex
    result = analyze_comment_hex()
    if result:
        print(f"\n🏆 TREASURE DISCOVERED: {result}")
        return
    
    # Analyze hex chunks
    result = analyze_hex_chunks()
    if result:
        print(f"\n🏆 TREASURE DISCOVERED: {result}")
        return
    
    print("\n💔 No treasure found in comment hex analysis")
    print("🔍 The private key might be hidden elsewhere...")

if __name__ == "__main__":
    main()
