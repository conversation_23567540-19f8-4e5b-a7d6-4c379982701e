import hashlib
import base58
import binascii
from ecdsa import Signing<PERSON><PERSON>, SECP256k1

def try_passphrase_as_private_key(passphrase, encoding='utf-8'):
    """Try to derive a private key from a passphrase using various methods"""
    # Method 1: Direct SHA-256 hash of the passphrase
    sha256_hash = hashlib.sha256(passphrase.encode(encoding)).hexdigest()
    
    # Method 2: Double SHA-256
    double_sha256 = hashlib.sha256(hashlib.sha256(passphrase.encode(encoding)).digest()).hexdigest()
    
    # Method 3: SHA-256 with nonce appended
    nonce = 44481
    nonce_bytes = nonce.to_bytes(4, byteorder='little')
    sha256_with_nonce = hashlib.sha256(passphrase.encode(encoding) + nonce_bytes).hexdigest()
    
    # Method 4: HMAC-SHA256 with nonce as key
    import hmac
    hmac_sha256 = hmac.new(nonce_bytes, passphrase.encode(encoding), hashlib.sha256).hexdigest()
    
    return {
        "sha256": sha256_hash,
        "double_sha256": double_sha256,
        "sha256_with_nonce": sha256_with_nonce,
        "hmac_sha256": hmac_sha256
    }

def private_key_to_wif(private_key_hex, compressed=True, testnet=False):
    """Convert a private key to WIF format"""
    # Add version byte (0x80 for mainnet, 0xef for testnet)
    version = b'\xef' if testnet else b'\x80'
    
    # Convert hex to bytes
    private_key_bytes = bytes.fromhex(private_key_hex)
    
    # Add compression flag if needed
    if compressed:
        extended_key = version + private_key_bytes + b'\x01'
    else:
        extended_key = version + private_key_bytes
    
    # Double SHA-256 for checksum
    checksum = hashlib.sha256(hashlib.sha256(extended_key).digest()).digest()[:4]
    
    # Combine and convert to Base58
    wif = base58.b58encode(extended_key + checksum).decode('utf-8')
    
    return wif

# Try with the timestamp from the genesis block
passphrase = "Nintondo"
private_key_candidates = try_passphrase_as_private_key(passphrase)

print("Potential private keys derived from 'Nintondo':")
for method, private_key in private_key_candidates.items():
    print(f"{method}: {private_key}")
    print(f"WIF (compressed): {private_key_to_wif(private_key)}")
    print(f"WIF (uncompressed): {private_key_to_wif(private_key, compressed=False)}")
    print()

# Try with other potential passphrases
other_passphrases = [
    "Bells",
    "BellsCoin",
    "Dogecoin",
    "Nintondo44481",
    "c0c0c0c0"  # Message start bytes
]

for phrase in other_passphrases:
    print(f"Trying passphrase: {phrase}")
    candidates = try_passphrase_as_private_key(phrase)
    for method, private_key in candidates.items():
        print(f"{method}: {private_key[:16]}...")  # Show just the beginning for brevity
    print()