<?xml version="1.0" encoding="UTF-8"?>
<ui version="4.0">
 <class>MiningPage</class>
 <widget class="QWidget" name="MiningPage">
  <property name="geometry">
   <rect>
    <x>0</x>
    <y>0</y>
    <width>780</width>
    <height>636</height>
   </rect>
  </property>
  <property name="windowTitle">
   <string>Mining</string>
  </property>
  <layout class="QGridLayout" name="gridLayout">
   <property name="sizeConstraint">
    <enum>QLayout::SetMaximumSize</enum>
   </property>
   <item row="7" column="1">
    <widget class="QLineEdit" name="serverLine">
     <property name="enabled">
      <bool>true</bool>
     </property>
    </widget>
   </item>
   <item row="6" column="3">
    <widget class="QLabel" name="usernameLabel">
     <property name="enabled">
      <bool>true</bool>
     </property>
     <property name="text">
      <string>Username</string>
     </property>
    </widget>
   </item>
   <item row="6" column="1">
    <widget class="QLabel" name="serverLabel">
     <property name="enabled">
      <bool>true</bool>
     </property>
     <property name="text">
      <string>Server</string>
     </property>
    </widget>
   </item>
   <item row="7" column="3">
    <widget class="QLineEdit" name="usernameLine">
     <property name="enabled">
      <bool>true</bool>
     </property>
     <property name="text">
      <string/>
     </property>
    </widget>
   </item>
   <item row="6" column="4">
    <widget class="QLabel" name="passwordLabel">
     <property name="enabled">
      <bool>true</bool>
     </property>
     <property name="text">
      <string>Password</string>
     </property>
    </widget>
   </item>
   <item row="6" column="2">
    <widget class="QLabel" name="portLabel">
     <property name="enabled">
      <bool>true</bool>
     </property>
     <property name="text">
      <string>Port</string>
     </property>
    </widget>
   </item>
   <item row="3" column="4">
    <widget class="QPushButton" name="startButton">
     <property name="sizePolicy">
      <sizepolicy hsizetype="Fixed" vsizetype="Fixed">
       <horstretch>0</horstretch>
       <verstretch>0</verstretch>
      </sizepolicy>
     </property>
     <property name="text">
      <string>Start Mining</string>
     </property>
    </widget>
   </item>
   <item row="8" column="1" colspan="4">
    <widget class="Line" name="line">
     <property name="sizePolicy">
      <sizepolicy hsizetype="Minimum" vsizetype="Fixed">
       <horstretch>0</horstretch>
       <verstretch>0</verstretch>
      </sizepolicy>
     </property>
     <property name="minimumSize">
      <size>
       <width>0</width>
       <height>20</height>
      </size>
     </property>
     <property name="orientation">
      <enum>Qt::Horizontal</enum>
     </property>
    </widget>
   </item>
   <item row="13" column="1" colspan="4">
    <widget class="QListWidget" name="list">
     <property name="autoScrollMargin">
      <number>100000</number>
     </property>
     <property name="dragDropMode">
      <enum>QAbstractItemView::DragOnly</enum>
     </property>
     <property name="selectionMode">
      <enum>QAbstractItemView::NoSelection</enum>
     </property>
     <property name="movement">
      <enum>QListView::Snap</enum>
     </property>
     <property name="batchSize">
      <number>10</number>
     </property>
    </widget>
   </item>
   <item row="14" column="1" colspan="2">
    <widget class="QLabel" name="shareCount">
     <property name="font">
      <font>
       <pointsize>10</pointsize>
       <weight>75</weight>
       <bold>true</bold>
      </font>
     </property>
     <property name="text">
      <string/>
     </property>
    </widget>
   </item>
   <item row="3" column="3">
    <widget class="QLabel" name="scantimeLabel">
     <property name="enabled">
      <bool>true</bool>
     </property>
     <property name="layoutDirection">
      <enum>Qt::LeftToRight</enum>
     </property>
     <property name="text">
      <string>Scantime</string>
     </property>
    </widget>
   </item>
   <item row="3" column="2">
    <widget class="QLabel" name="threadsLabel">
     <property name="enabled">
      <bool>true</bool>
     </property>
     <property name="text">
      <string>Threads</string>
     </property>
    </widget>
   </item>
   <item row="4" column="4">
    <widget class="QCheckBox" name="debugCheckBox">
     <property name="text">
      <string>Debug Logging</string>
     </property>
    </widget>
   </item>
   <item row="3" column="1">
    <widget class="QLabel" name="typeLabel">
     <property name="text">
      <string>Type</string>
     </property>
    </widget>
   </item>
   <item row="4" column="2">
    <widget class="QSpinBox" name="threadsBox">
     <property name="enabled">
      <bool>true</bool>
     </property>
     <property name="minimum">
      <number>1</number>
     </property>
     <property name="maximum">
      <number>99</number>
     </property>
     <property name="singleStep">
      <number>1</number>
     </property>
    </widget>
   </item>
   <item row="4" column="1">
    <widget class="QComboBox" name="typeBox">
     <property name="editable">
      <bool>false</bool>
     </property>
     <property name="currentIndex">
      <number>0</number>
     </property>
     <item>
      <property name="text">
       <string>Solo Mining</string>
      </property>
     </item>
     <item>
      <property name="text">
       <string>Pool Mining</string>
      </property>
     </item>
    </widget>
   </item>
   <item row="4" column="3">
    <widget class="QSpinBox" name="scantimeBox">
     <property name="enabled">
      <bool>true</bool>
     </property>
     <property name="minimum">
      <number>1</number>
     </property>
     <property name="value">
      <number>5</number>
     </property>
    </widget>
   </item>
   <item row="7" column="2">
    <widget class="QLineEdit" name="portLine">
     <property name="enabled">
      <bool>true</bool>
     </property>
     <property name="text">
      <string>9332</string>
     </property>
     <property name="value" stdset="0">
      <number>9332</number>
     </property>
    </widget>
   </item>
   <item row="7" column="4">
    <widget class="QLineEdit" name="passwordLine">
     <property name="enabled">
      <bool>true</bool>
     </property>
    </widget>
   </item>
   <item row="14" column="3" colspan="2">
    <widget class="QLabel" name="mineSpeedLabel">
     <property name="sizePolicy">
      <sizepolicy hsizetype="Preferred" vsizetype="Preferred">
       <horstretch>0</horstretch>
       <verstretch>0</verstretch>
      </sizepolicy>
     </property>
     <property name="font">
      <font>
       <pointsize>10</pointsize>
       <weight>75</weight>
       <bold>true</bold>
      </font>
     </property>
     <property name="text">
      <string/>
     </property>
     <property name="alignment">
      <set>Qt::AlignRight|Qt::AlignTrailing|Qt::AlignVCenter</set>
     </property>
    </widget>
   </item>
  </layout>
 </widget>
 <layoutdefault spacing="6" margin="11"/>
 <tabstops>
  <tabstop>startButton</tabstop>
  <tabstop>typeBox</tabstop>
  <tabstop>threadsBox</tabstop>
  <tabstop>scantimeBox</tabstop>
  <tabstop>serverLine</tabstop>
  <tabstop>portLine</tabstop>
  <tabstop>usernameLine</tabstop>
  <tabstop>passwordLine</tabstop>
  <tabstop>debugCheckBox</tabstop>
  <tabstop>list</tabstop>
 </tabstops>
 <resources/>
 <connections/>
</ui>
