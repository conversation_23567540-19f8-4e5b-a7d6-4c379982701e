<?xml version="1.0" encoding="UTF-8"?>
<ui version="4.0">
 <class>QRCodeDialog</class>
 <widget class="QDialog" name="QRCodeDialog">
  <property name="geometry">
   <rect>
    <x>0</x>
    <y>0</y>
    <width>340</width>
    <height>530</height>
   </rect>
  </property>
  <property name="windowTitle">
   <string>QR Code Dialog</string>
  </property>
  <layout class="QVBoxLayout" name="verticalLayout_3">
   <item>
    <widget class="QLabel" name="lblQRCode">
     <property name="sizePolicy">
      <sizepolicy hsizetype="Expanding" vsizetype="Expanding">
       <horstretch>0</horstretch>
       <verstretch>0</verstretch>
      </sizepolicy>
     </property>
     <property name="minimumSize">
      <size>
       <width>300</width>
       <height>300</height>
      </size>
     </property>
     <property name="textFormat">
      <enum>Qt::PlainText</enum>
     </property>
     <property name="alignment">
      <set>Qt::AlignCenter</set>
     </property>
     <property name="wordWrap">
      <bool>true</bool>
     </property>
    </widget>
   </item>
   <item>
    <widget class="QPlainTextEdit" name="outUri">
     <property name="sizePolicy">
      <sizepolicy hsizetype="Expanding" vsizetype="Minimum">
       <horstretch>0</horstretch>
       <verstretch>0</verstretch>
      </sizepolicy>
     </property>
     <property name="minimumSize">
      <size>
       <width>0</width>
       <height>50</height>
      </size>
     </property>
     <property name="tabChangesFocus">
      <bool>true</bool>
     </property>
     <property name="textInteractionFlags">
      <set>Qt::TextSelectableByKeyboard|Qt::TextSelectableByMouse</set>
     </property>
    </widget>
   </item>
   <item>
    <widget class="QWidget" name="widget" native="true">
     <layout class="QVBoxLayout" name="verticalLayout_2">
      <item>
       <widget class="QCheckBox" name="chkReqPayment">
        <property name="enabled">
         <bool>true</bool>
        </property>
        <property name="text">
         <string>Request Payment</string>
        </property>
       </widget>
      </item>
      <item>
       <layout class="QFormLayout" name="formLayout">
        <property name="fieldGrowthPolicy">
         <enum>QFormLayout::AllNonFixedFieldsGrow</enum>
        </property>
        <item row="1" column="0">
         <widget class="QLabel" name="lblLabel">
          <property name="text">
           <string>Label:</string>
          </property>
          <property name="textFormat">
           <enum>Qt::PlainText</enum>
          </property>
          <property name="alignment">
           <set>Qt::AlignRight|Qt::AlignTrailing|Qt::AlignVCenter</set>
          </property>
          <property name="buddy">
           <cstring>lnLabel</cstring>
          </property>
         </widget>
        </item>
        <item row="1" column="1">
         <widget class="QLineEdit" name="lnLabel"/>
        </item>
        <item row="2" column="0">
         <widget class="QLabel" name="lblMessage">
          <property name="text">
           <string>Message:</string>
          </property>
          <property name="textFormat">
           <enum>Qt::PlainText</enum>
          </property>
          <property name="alignment">
           <set>Qt::AlignRight|Qt::AlignTrailing|Qt::AlignVCenter</set>
          </property>
          <property name="buddy">
           <cstring>lnMessage</cstring>
          </property>
         </widget>
        </item>
        <item row="2" column="1">
         <widget class="QLineEdit" name="lnMessage"/>
        </item>
        <item row="0" column="0">
         <widget class="QLabel" name="lblAmount">
          <property name="sizePolicy">
           <sizepolicy hsizetype="Preferred" vsizetype="Maximum">
            <horstretch>0</horstretch>
            <verstretch>0</verstretch>
           </sizepolicy>
          </property>
          <property name="text">
           <string>Amount:</string>
          </property>
          <property name="textFormat">
           <enum>Qt::PlainText</enum>
          </property>
          <property name="alignment">
           <set>Qt::AlignRight|Qt::AlignTrailing|Qt::AlignVCenter</set>
          </property>
          <property name="buddy">
           <cstring>lnReqAmount</cstring>
          </property>
         </widget>
        </item>
        <item row="0" column="1">
         <widget class="BitcoinAmountField" name="lnReqAmount">
          <property name="enabled">
           <bool>false</bool>
          </property>
          <property name="minimumSize">
           <size>
            <width>80</width>
            <height>0</height>
           </size>
          </property>
         </widget>
        </item>
       </layout>
      </item>
      <item>
       <layout class="QHBoxLayout" name="horizontalLayout">
        <item>
         <spacer name="horizontalSpacer">
          <property name="orientation">
           <enum>Qt::Horizontal</enum>
          </property>
          <property name="sizeHint" stdset="0">
           <size>
            <width>40</width>
            <height>20</height>
           </size>
          </property>
         </spacer>
        </item>
        <item>
         <widget class="QPushButton" name="btnSaveAs">
          <property name="text">
           <string>&amp;Save As...</string>
          </property>
         </widget>
        </item>
       </layout>
      </item>
     </layout>
    </widget>
   </item>
  </layout>
 </widget>
 <customwidgets>
  <customwidget>
   <class>BitcoinAmountField</class>
   <extends>QSpinBox</extends>
   <header>bitcoinamountfield.h</header>
  </customwidget>
 </customwidgets>
 <resources/>
 <connections>
  <connection>
   <sender>chkReqPayment</sender>
   <signal>clicked(bool)</signal>
   <receiver>lnReqAmount</receiver>
   <slot>setEnabled(bool)</slot>
   <hints>
    <hint type="sourcelabel">
     <x>92</x>
     <y>285</y>
    </hint>
    <hint type="destinationlabel">
     <x>98</x>
     <y>311</y>
    </hint>
   </hints>
  </connection>
 </connections>
</ui>
