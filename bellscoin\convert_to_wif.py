#!/usr/bin/env python3
"""
Convert Private Key to WIF Format
=================================

Converts the discovered private key to Wallet Import Format (WIF)
for use in Bitcoin wallets.
"""

import hashlib

def private_key_to_wif(private_key_hex: str, compressed: bool = False, testnet: bool = False) -> str:
    """Convert private key to WIF format"""
    
    # Add version byte
    if testnet:
        version_byte = b'\xef'  # Testnet
    else:
        version_byte = b'\x80'  # Mainnet
    
    # Convert hex to bytes
    private_key_bytes = bytes.fromhex(private_key_hex)
    
    # Add compression flag if needed
    if compressed:
        extended_key = version_byte + private_key_bytes + b'\x01'
    else:
        extended_key = version_byte + private_key_bytes
    
    # Double SHA256 for checksum
    checksum = hashlib.sha256(hashlib.sha256(extended_key).digest()).digest()[:4]
    
    # Combine
    wif_bytes = extended_key + checksum
    
    # Base58 encode
    alphabet = "**********************************************************"
    num = int.from_bytes(wif_bytes, 'big')
    
    if num == 0:
        return alphabet[0]
    
    result = ""
    while num > 0:
        num, remainder = divmod(num, 58)
        result = alphabet[remainder] + result
    
    # Add leading zeros
    for byte in wif_bytes:
        if byte == 0:
            result = alphabet[0] + result
        else:
            break
    
    return result

# The discovered private key
PRIVATE_KEY = "e5be24df57c43a82d15c2f06bda961296948f8f8eb48501bed1efb929afe0698"

def main():
    print("🔑 PRIVATE KEY TO WIF CONVERTER")
    print("=" * 50)
    print(f"Private Key: {PRIVATE_KEY}")
    print()
    
    # Generate different WIF formats
    wif_uncompressed = private_key_to_wif(PRIVATE_KEY, compressed=False, testnet=False)
    wif_compressed = private_key_to_wif(PRIVATE_KEY, compressed=True, testnet=False)
    wif_testnet = private_key_to_wif(PRIVATE_KEY, compressed=False, testnet=True)
    
    print("📋 WIF FORMATS:")
    print(f"Uncompressed (Mainnet): {wif_uncompressed}")
    print(f"Compressed (Mainnet):   {wif_compressed}")
    print(f"Testnet:                {wif_testnet}")
    print()
    
    print("💡 USAGE INSTRUCTIONS:")
    print("1. Use the 'Uncompressed (Mainnet)' format for most wallets")
    print("2. Import into Electrum: File → New/Restore → Import addresses/keys")
    print("3. Import into Bitcoin Core: bitcoin-cli importprivkey 'WIF_KEY'")
    print()
    
    print("⚠️  SECURITY WARNING:")
    print("This key is now PUBLIC and should only be used for testing!")
    print("Never send real funds to addresses generated from this key!")

if __name__ == "__main__":
    main()
