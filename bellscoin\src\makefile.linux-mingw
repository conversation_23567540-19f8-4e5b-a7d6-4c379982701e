# Copyright (c) 2009-2010 <PERSON><PERSON>
# Distributed under the MIT/X11 software license, see the accompanying
# file license.txt or http://www.opensource.org/licenses/mit-license.php.

DEPSDIR:=/usr/i586-mingw32msvc

USE_UPNP:=0

INCLUDEPATHS= \
 -I"$(DEPSDIR)/boost_1_53_0" \
 -I"$(DEPSDIR)/db-4.8.30.NC/build_unix" \
 -I"$(DEPSDIR)/openssl-1.0.1e/include" \
 -I"$(CURDIR)"/obj \

LIBPATHS= \
 -L"$(DEPSDIR)/boost_1_53_0/stage/lib" \
 -L"$(DEPSDIR)/db-4.8.30.NC/build_unix" \
 -L"$(DEPSDIR)/openssl-1.0.1e"

LIBS= \
 -l boost_system-mt-s \
 -l boost_filesystem-mt-s \
 -l boost_program_options-mt-s \
 -l boost_thread_win32-mt-s \
 -l db_cxx \
 -l ssl \
 -l crypto

DEFS=-D_MT -DWIN32 -D_WINDOWS -DBOOST_THREAD_USE_LIB -DBOOST_SPIRIT_THREADSAFE -DUSE_IPV6
DEBUGFLAGS=-g
CFLAGS=-O2 -w -Wall -Wextra -Wformat -Wformat-security -Wno-unused-parameter $(DEBUGFLAGS) $(DEFS) $(INCLUDEPATHS)

TESTDEFS = -DTEST_DATA_DIR=$(abspath test/data)

ifdef USE_UPNP
	LIBPATHS += -L"$(DEPSDIR)/miniupnpc"
	LIBS += -l miniupnpc -l iphlpapi
	DEFS += -DSTATICLIB -DUSE_UPNP=$(USE_UPNP)
endif

LIBS += -l mingwthrd -l kernel32 -l user32 -l gdi32 -l comdlg32 -l winspool -l winmm -l shell32 -l comctl32 -l ole32 -l oleaut32 -l uuid -l rpcrt4 -l advapi32 -l ws2_32 -l mswsock -l shlwapi

# TODO: make the mingw builds smarter about dependencies, like the linux/osx builds are
HEADERS = $(wildcard *.h)

OBJS= \
    obj/version.o \
    obj/checkpoints.o \
    obj/netbase.o \
    obj/addrman.o \
    obj/crypter.o \
    obj/key.o \
    obj/db.o \
    obj/init.o \
    obj/irc.o \
    obj/keystore.o \
    obj/main.o \
    obj/net.o \
    obj/protocol.o \
    obj/bitcoinrpc.o \
    obj/rpcdump.o \
    obj/rpcnet.o \
    obj/rpcrawtransaction.o \
    obj/script.o \
    obj/scrypt.o \
    obj/sync.o \
    obj/util.o \
    obj/wallet.o \
    obj/walletdb.o \
    obj/noui.o

all: bellsd.exe

obj/scrypt.o: scrypt.c
	i586-mingw32msvc-gcc -c $(CFLAGS) -o $@ $^

obj/build.h: FORCE
	/bin/sh ../share/genbuild.sh obj/build.h
version.cpp: obj/build.h
DEFS += -DHAVE_BUILD_INFO

obj/%.o: %.cpp $(HEADERS)
	i586-mingw32msvc-g++ -c $(CFLAGS) -o $@ $<

bellsd.exe: $(OBJS:obj/%=obj/%)
	i586-mingw32msvc-g++ $(CFLAGS) -o $@ $(LIBPATHS) $^ $(LIBS)

TESTOBJS := $(patsubst test/%.cpp,obj-test/%.o,$(wildcard test/*.cpp))

obj-test/%.o: test/%.cpp $(HEADERS)
	i586-mingw32msvc-g++ -c $(TESTDEFS) $(CFLAGS) -o $@ $<

test_bells.exe: $(TESTOBJS) $(filter-out obj/init.o,$(OBJS:obj/%=obj/%))
	i586-mingw32msvc-g++ $(CFLAGS) -o $@ $(LIBPATHS) $^ -lboost_unit_test_framework $(LIBS)


clean:
	-rm -f obj/*.o
	-rm -f bellsd.exe
	-rm -f obj-test/*.o
	-rm -f test_bells.exe
	-rm -f src/build.h

FORCE:
